import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  FileText,
  Save,
  ArrowLeft,
  Edit,
  Eye,
  Sparkles,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  Calendar,
  BarChart3,
  Settings,
  Download,
  Share2
} from 'lucide-react';
import {
  BusinessPlan,
  BusinessPlanSection,
  businessPlansAPI,
  businessPlanSectionsAPI
} from '../../services/businessPlanApi';

interface BusinessPlanEditorProps {
  businessPlanId: number;
  onSave?: (plan: BusinessPlan) => void;
  onBack?: () => void;
  readOnly?: boolean;
}

const BusinessPlanEditor: React.FC<BusinessPlanEditorProps> = ({
  businessPlanId,
  onSave,
  onBack,
  readOnly = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // State management
  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [sections, setSections] = useState<BusinessPlanSection[]>([]);
  const [activeSectionId, setActiveSectionId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Section content state
  const [sectionContent, setSectionContent] = useState<{ [key: number]: string }>({});
  const [generatingContent, setGeneratingContent] = useState<number | null>(null);

  // Auto-save
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  useEffect(() => {
    loadBusinessPlan();
  }, [businessPlanId]);

  useEffect(() => {
    // Auto-save functionality
    if (hasUnsavedChanges && !readOnly) {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
      
      const timer = setTimeout(() => {
        handleAutoSave();
      }, 3000); // Auto-save after 3 seconds of inactivity
      
      setAutoSaveTimer(timer);
    }

    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
    };
  }, [hasUnsavedChanges, sectionContent]);

  const loadBusinessPlan = async () => {
    try {
      setLoading(true);
      setError(null);

      const [planData, sectionsData] = await Promise.all([
        businessPlansAPI.getPlan(businessPlanId),
        businessPlanSectionsAPI.getSections(businessPlanId)
      ]);

      setBusinessPlan(planData);
      setSections(sectionsData);

      // Initialize section content
      const contentMap: { [key: number]: string } = {};
      sectionsData.forEach(section => {
        contentMap[section.id] = section.content || '';
      });
      setSectionContent(contentMap);

      // Set first section as active
      if (sectionsData.length > 0) {
        setActiveSectionId(sectionsData[0].id);
      }
    } catch (err) {
      setError(t('businessPlan.loadError'));
      console.error('Error loading business plan:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoSave = useCallback(async () => {
    if (!hasUnsavedChanges || readOnly) return;

    try {
      await saveCurrentSection();
      setHasUnsavedChanges(false);
      setLastSaved(new Date());
    } catch (err) {
      console.error('Auto-save failed:', err);
    }
  }, [hasUnsavedChanges, activeSectionId, sectionContent]);

  const saveCurrentSection = async () => {
    if (!activeSectionId || readOnly) return;

    const section = sections.find(s => s.id === activeSectionId);
    if (!section) return;

    const content = sectionContent[activeSectionId] || '';
    
    await businessPlanSectionsAPI.updateSection(activeSectionId, {
      content,
      is_completed: content.trim().length > 0
    });
  };

  const handleSectionSelect = (sectionId: number) => {
    if (hasUnsavedChanges) {
      handleAutoSave();
    }
    setActiveSectionId(sectionId);
  };

  const handleContentChange = (content: string) => {
    if (readOnly || !activeSectionId) return;

    setSectionContent(prev => ({
      ...prev,
      [activeSectionId]: content
    }));
    setHasUnsavedChanges(true);
  };

  const handleGenerateContent = async (sectionId: number) => {
    if (readOnly) return;

    try {
      setGeneratingContent(sectionId);
      const section = sections.find(s => s.id === sectionId);
      if (!section) return;

      const generatedContent = await businessPlanSectionsAPI.generateContent(sectionId);
      
      setSectionContent(prev => ({
        ...prev,
        [sectionId]: generatedContent.content
      }));
      setHasUnsavedChanges(true);
    } catch (err) {
      setError(t('businessPlan.generateError'));
      console.error('Error generating content:', err);
    } finally {
      setGeneratingContent(null);
    }
  };

  const handleSave = async () => {
    if (readOnly) return;

    try {
      setSaving(true);
      setError(null);

      // Save current section first
      await saveCurrentSection();

      // Update business plan metadata if needed
      if (businessPlan) {
        const updatedPlan = await businessPlansAPI.updatePlan(businessPlan.id, {
          title: businessPlan.title,
          status: businessPlan.status
        });

        setBusinessPlan(updatedPlan);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());

        if (onSave) {
          onSave(updatedPlan);
        }
      }
    } catch (err) {
      setError(t('businessPlan.saveError'));
      console.error('Error saving business plan:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    if (hasUnsavedChanges) {
      if (window.confirm(t('businessPlan.unsavedChanges'))) {
        if (onBack) {
          onBack();
        } else {
          navigate('/dashboard/business-plans');
        }
      }
    } else {
      if (onBack) {
        onBack();
      } else {
        navigate('/dashboard/business-plans');
      }
    }
  };

  const getCompletionPercentage = () => {
    if (sections.length === 0) return 0;
    const completedSections = sections.filter(s => 
      sectionContent[s.id] && sectionContent[s.id].trim().length > 0
    ).length;
    return Math.round((completedSections / sections.length) * 100);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleTimeString(isRTL ? 'ar' : 'en', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{t('businessPlan.loading')}</p>
        </div>
      </div>
    );
  }

  if (!businessPlan) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
        <div className="text-center">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('businessPlan.notFound')}
          </h3>
          <p className="text-gray-400 mb-6">
            {t('businessPlan.notFoundDescription')}
          </p>
          <button
            onClick={handleBack}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            {t('common.goBack')}
          </button>
        </div>
      </div>
    );
  }

  const activeSection = sections.find(s => s.id === activeSectionId);
  const completionPercentage = getCompletionPercentage();

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600/20 to-indigo-600/20 p-6 border-b border-white/10">
        <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={handleBack}
              className={`text-gray-400 hover:text-white transition-colors ${isRTL ? 'ml-4' : 'mr-4'}`}
            >
              <ArrowLeft size={20} />
            </button>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FileText size={24} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
              <div>
                <h1 className="text-xl font-bold text-white">{businessPlan.title}</h1>
                <div className={`flex items-center text-sm text-gray-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span>{t('businessPlan.status')}: {t(`businessPlan.status.${businessPlan.status}`)}</span>
                  <span className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                    {completionPercentage}% {t('common.complete')}
                  </span>
                  {lastSaved && (
                    <span className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                      {t('common.lastSaved')}: {formatDate(lastSaved)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {!readOnly && (
              <>
                <button
                  onClick={handleSave}
                  disabled={saving || !hasUnsavedChanges}
                  className={`bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center ${
                    isRTL ? 'flex-row-reverse' : ''
                  }`}
                >
                  {saving ? (
                    <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                  ) : (
                    <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  )}
                  <span>{saving ? t('common.saving') : t('common.save')}</span>
                </button>
              </>
            )}
            
            <button
              onClick={() => {/* TODO: Implement download */}}
              className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
              title={t('common.download')}
            >
              <Download size={16} />
            </button>
            
            <button
              onClick={() => {/* TODO: Implement share */}}
              className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
              title={t('common.share')}
            >
              <Share2 size={16} />
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-6 border-b border-white/10">
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertCircle size={20} className={`text-red-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <span className="text-red-300">{error}</span>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="flex h-[calc(100vh-300px)]">
        {/* Sections Sidebar */}
        <div className="w-1/4 border-r border-white/10 bg-gray-800/30">
          <div className="p-4 border-b border-white/10">
            <h3 className="text-lg font-semibold text-white">{t('businessPlan.sections')}</h3>
          </div>
          <div className="overflow-y-auto h-full">
            {sections.map((section, index) => {
              const isActive = section.id === activeSectionId;
              const isCompleted = sectionContent[section.id] && sectionContent[section.id].trim().length > 0;
              
              return (
                <button
                  key={section.id}
                  onClick={() => handleSectionSelect(section.id)}
                  className={`w-full p-4 text-left border-b border-white/5 transition-colors ${
                    isActive 
                      ? 'bg-purple-600/20 border-r-2 border-r-purple-500' 
                      : 'hover:bg-gray-700/50'
                  } ${isRTL ? 'text-right' : ''}`}
                >
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-1">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={`text-sm text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          {index + 1}.
                        </span>
                        <span className={`text-white font-medium ${isActive ? 'text-purple-300' : ''}`}>
                          {section.title}
                        </span>
                      </div>
                      {section.is_required && (
                        <span className="text-xs text-red-400">{t('common.required')}</span>
                      )}
                    </div>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      {generatingContent === section.id ? (
                        <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : isCompleted ? (
                        <CheckCircle size={16} className="text-green-400" />
                      ) : (
                        <div className="w-4 h-4 border-2 border-gray-600 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Section Editor */}
        <div className="flex-1 flex flex-col">
          {activeSection ? (
            <>
              {/* Section Header */}
              <div className="p-6 border-b border-white/10">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <h2 className="text-xl font-bold text-white">{activeSection.title}</h2>
                    {activeSection.description && (
                      <p className="text-gray-400 text-sm mt-1">{activeSection.description}</p>
                    )}
                  </div>
                  
                  {!readOnly && (
                    <button
                      onClick={() => handleGenerateContent(activeSection.id)}
                      disabled={generatingContent === activeSection.id}
                      className={`bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center ${
                        isRTL ? 'flex-row-reverse' : ''
                      }`}
                    >
                      {generatingContent === activeSection.id ? (
                        <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                      ) : (
                        <Sparkles size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                      )}
                      <span>
                        {generatingContent === activeSection.id 
                          ? t('businessPlan.generating') 
                          : t('businessPlan.generateWithAI')
                        }
                      </span>
                    </button>
                  )}
                </div>
              </div>

              {/* Section Content Editor */}
              <div className="flex-1 p-6">
                <textarea
                  value={sectionContent[activeSection.id] || ''}
                  onChange={(e) => handleContentChange(e.target.value)}
                  placeholder={t('businessPlan.sectionPlaceholder', { title: activeSection.title })}
                  className={`w-full h-full bg-gray-800/50 border border-gray-600 rounded-lg p-4 text-white placeholder-gray-400 resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    isRTL ? 'text-right' : ''
                  }`}
                  disabled={readOnly}
                />
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText size={48} className="mx-auto mb-4 text-gray-500" />
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('businessPlan.selectSection')}
                </h3>
                <p className="text-gray-400">
                  {t('businessPlan.selectSectionDescription')}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Auto-save indicator */}
      {hasUnsavedChanges && !readOnly && (
        <div className="absolute bottom-4 right-4 bg-yellow-600/20 border border-yellow-600/50 rounded-lg px-3 py-2">
          <div className={`flex items-center text-yellow-300 text-sm ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Clock size={14} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
            <span>{t('businessPlan.unsavedChanges')}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessPlanEditor;
