#!/usr/bin/env python
"""
Simple Django test script to diagnose issues
"""

import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())

try:
    print("Testing basic imports...")
    import django
    print("✅ Django imported successfully, version:", django.get_version())
except ImportError as e:
    print("❌ Django import failed:", e)
    sys.exit(1)

try:
    from dotenv import load_dotenv
    print("✅ python-dotenv imported successfully")
except ImportError as e:
    print("❌ python-dotenv import failed:", e)

try:
    import rest_framework
    print("✅ Django REST Framework imported successfully")
except ImportError as e:
    print("❌ Django REST Framework import failed:", e)

try:
    import corsheaders
    print("✅ django-cors-headers imported successfully")
except ImportError as e:
    print("❌ django-cors-headers import failed:", e)

# Test Django setup
try:
    print("\nTesting Django setup...")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
    
    # Load environment variables
    load_dotenv('.env')
    print("✅ Environment variables loaded")
    
    # Setup Django
    django.setup()
    print("✅ Django setup successful!")
    
    # Test database connection
    from django.db import connection
    cursor = connection.cursor()
    print("✅ Database connection successful")
    
    # Test a simple query
    cursor.execute("SELECT 1")
    result = cursor.fetchone()
    print("✅ Database query successful:", result)
    
except Exception as e:
    print("❌ Django setup failed:", str(e))
    import traceback
    traceback.print_exc()

print("\nDiagnostic complete!")
