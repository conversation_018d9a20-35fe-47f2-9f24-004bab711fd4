/**
 * Authentication Guard Component
 * Ensures proper authentication state and handles token validation
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { getAuthToken, getRefreshToken, authAPI } from '../../services/api';
import { useTranslation } from 'react-i18next';

interface AuthenticationGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  fallback?: React.ReactNode;
}

interface AuthStatus {
  hasTokens: boolean;
  tokensValid: boolean;
  userLoaded: boolean;
  isChecking: boolean;
  error?: string;
}

export const AuthenticationGuard: React.FC<AuthenticationGuardProps> = ({
  children,
  requireAuth = true,
  fallback
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { t } = useTranslation();
  const [authStatus, setAuthStatus] = useState<AuthStatus>({
    hasTokens: false,
    tokensValid: false,
    userLoaded: false,
    isChecking: true
  });

  /**
   * Comprehensive authentication check
   */
  useEffect(() => {
    const checkAuthStatus = async () => {
      setAuthStatus(prev => ({ ...prev, isChecking: true, error: undefined }));

      try {
        const token = getAuthToken();
        const refreshToken = getRefreshToken();
        const hasTokens = !!(token && refreshToken);

        if (!hasTokens) {
          setAuthStatus({
            hasTokens: false,
            tokensValid: false,
            userLoaded: false,
            isChecking: false
          });
          return;
        }

        // Validate token with server
        const tokensValid = await authAPI.verifyToken();
        
        setAuthStatus({
          hasTokens,
          tokensValid,
          userLoaded: !!user,
          isChecking: false
        });

      } catch (error) {
        console.error('Auth status check failed:', error);
        setAuthStatus({
          hasTokens: false,
          tokensValid: false,
          userLoaded: false,
          isChecking: false,
          error: 'Authentication check failed'
        });
      }
    };

    checkAuthStatus();
  }, [user, isAuthenticated]);

  /**
   * Show loading state while checking authentication
   */
  if (authStatus.isChecking || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('auth.checking', 'Checking authentication...')}</p>
        </div>
      </div>
    );
  }

  /**
   * Handle authentication errors
   */
  if (authStatus.error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            {t('auth.error.title', 'Authentication Error')}
          </h3>
          <p className="text-red-700 mb-4">{authStatus.error}</p>
          <button
            onClick={() => window.location.href = '/login'}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
          >
            {t('auth.goToLogin', 'Go to Login')}
          </button>
        </div>
      </div>
    );
  }

  /**
   * Handle unauthenticated state
   */
  if (requireAuth && (!authStatus.hasTokens || !authStatus.tokensValid || !isAuthenticated)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="text-yellow-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            {t('auth.required.title', 'Authentication Required')}
          </h3>
          <p className="text-yellow-700 mb-4">
            {t('auth.required.message', 'Please log in to access this page.')}
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors"
          >
            {t('auth.login', 'Login')}
          </button>
        </div>
      </div>
    );
  }

  /**
   * Authentication successful, render children
   */
  return <>{children}</>;
};

/**
 * Debug component to show authentication status (development only)
 */
export const AuthDebugInfo: React.FC = () => {
  const { user, isAuthenticated, isLoading, error } = useAuth();
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  useEffect(() => {
    const token = getAuthToken();
    const refreshToken = getRefreshToken();
    
    let tokenData = null;
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        tokenData = {
          hasToken: true,
          hasRefreshToken: !!refreshToken,
          exp: new Date(payload.exp * 1000).toLocaleString(),
          userId: payload.user_id,
          isExpired: payload.exp < Math.floor(Date.now() / 1000)
        };
      } catch (e) {
        tokenData = { hasToken: true, hasRefreshToken: !!refreshToken, error: 'Invalid token format' };
      }
    } else {
      tokenData = { hasToken: false, hasRefreshToken: !!refreshToken };
    }
    
    setTokenInfo(tokenData);
  }, [user, isAuthenticated]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h4 className="font-bold mb-2">🔐 Auth Debug</h4>
      <div className="space-y-1">
        <div>Redux Auth: {isAuthenticated ? '✅' : '❌'}</div>
        <div>User: {user ? `${user.username} (${user.role})` : 'None'}</div>
        <div>Loading: {isLoading ? '⏳' : '✅'}</div>
        <div>Error: {error || 'None'}</div>
        <div>Token: {tokenInfo?.hasToken ? '✅' : '❌'}</div>
        <div>Refresh: {tokenInfo?.hasRefreshToken ? '✅' : '❌'}</div>
        {tokenInfo?.exp && <div>Expires: {tokenInfo.exp}</div>}
        {tokenInfo?.isExpired && <div className="text-red-400">⚠️ Token Expired</div>}
      </div>
    </div>
  );
};

export default AuthenticationGuard;
