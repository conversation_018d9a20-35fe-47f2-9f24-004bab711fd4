import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';

interface MissingTranslation {
  key: string;
  namespace: string;
  currentValue: string;
}

interface TranslationValidatorProps {
  enabled?: boolean;
  showInProduction?: boolean;
}

/**
 * Development tool to validate translation completeness
 * Only shows in development mode unless explicitly enabled
 */
const TranslationValidator: React.FC<TranslationValidatorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  showInProduction = false
}) => {
  const { t, i18n } = useTranslation();
  const [missingKeys, setMissingKeys] = useState<MissingTranslation[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Don't render in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !showInProduction && !enabled) {
    return null;
  }

  const validateTranslations = async () => {
    setIsValidating(true);
    
    try {
      const missing: MissingTranslation[] = [];
      const currentLanguage = i18n.language;
      const supportedLanguages = ['en', 'ar'];
      
      // Get all translation keys from the current language
      const resources = i18n.getResourceBundle(currentLanguage, 'translation');
      
      if (resources) {
        const keys = extractKeysFromObject(resources);
        
        // Check each key in all supported languages
        for (const key of keys) {
          for (const lang of supportedLanguages) {
            const translation = i18n.getResourceBundle(lang, 'translation');
            const value = getNestedValue(translation, key);
            
            if (!value || value === key) {
              missing.push({
                key,
                namespace: lang,
                currentValue: value || '[MISSING]'
              });
            }
          }
        }
      }
      
      setMissingKeys(missing);
    } catch (error) {
      console.error('Translation validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const extractKeysFromObject = (obj: any, prefix = ''): string[] => {
    const keys: string[] = [];
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          keys.push(...extractKeysFromObject(obj[key], fullKey));
        } else {
          keys.push(fullKey);
        }
      }
    }
    
    return keys;
  };

  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  };

  useEffect(() => {
    if (enabled) {
      validateTranslations();
    }
  }, [i18n.language, enabled]);

  if (!enabled) return null;

  const missingCount = missingKeys.length;
  const hasIssues = missingCount > 0;

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      {/* Compact Status Indicator */}
      <div 
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg cursor-pointer transition-all
          ${hasIssues 
            ? 'bg-red-100 border border-red-300 text-red-800' 
            : 'bg-green-100 border border-green-300 text-green-800'
          }
        `}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isValidating ? (
          <RefreshCw className="w-4 h-4 animate-spin" />
        ) : hasIssues ? (
          <AlertTriangle className="w-4 h-4" />
        ) : (
          <CheckCircle className="w-4 h-4" />
        )}
        
        <span className="text-sm font-medium">
          {isValidating 
            ? 'Validating...' 
            : hasIssues 
              ? `${missingCount} Missing Keys`
              : 'Translations OK'
          }
        </span>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            validateTranslations();
          }}
          className="text-xs px-2 py-1 rounded bg-white/50 hover:bg-white/80 transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Expanded Details */}
      {isExpanded && hasIssues && (
        <div className="mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          <div className="p-3 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900">Missing Translations</h3>
            <p className="text-sm text-gray-600">
              {missingCount} keys need translation
            </p>
          </div>
          
          <div className="p-3 space-y-2">
            {missingKeys.slice(0, 20).map((missing, index) => (
              <div key={index} className="text-xs">
                <div className="font-mono text-red-600">{missing.key}</div>
                <div className="text-gray-500">
                  {missing.namespace}: {missing.currentValue}
                </div>
              </div>
            ))}
            
            {missingCount > 20 && (
              <div className="text-xs text-gray-500 pt-2 border-t">
                ... and {missingCount - 20} more
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationValidator;
