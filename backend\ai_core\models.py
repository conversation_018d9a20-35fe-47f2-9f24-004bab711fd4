"""
AI Core app - Django models for core AI functionality
"""
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class AISession(models.Model):
    """Track AI interaction sessions"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_type = models.CharField(max_length=50)  # 'advisor', 'idea_builder', etc.
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "AI Session"
        verbose_name_plural = "AI Sessions"
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.session_type} session for {self.user.username}"


class AIInteraction(models.Model):
    """Individual AI interactions within a session"""
    
    session = models.ForeignKey(AISession, on_delete=models.CASCADE, related_name='interactions')
    user_input = models.TextField()
    ai_response = models.TextField()
    interaction_type = models.Char<PERSON>ield(max_length=50)
    metadata = models.J<PERSON><PERSON><PERSON>(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "AI Interaction"
        verbose_name_plural = "AI Interactions"
        ordering = ['created_at']
    
    def __str__(self):
        return f"Interaction in {self.session.session_type} session"
