import { apiClient } from './apiClient';

export interface InvestorStats {
  portfolioValue: number;
  totalInvested: number;
  totalReturn: number;
  returnPercentage: number;
  activeInvestments: number;
  pendingDeals: number;
  monthlyReturn: number;
  irr: number;
}

export interface Investment {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  investmentAmount: number;
  currentValue: number;
  performance: number;
  lastUpdate: string;
  status: string;
}

export interface Deal {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  fundingRound: string;
  askAmount: number;
  valuation: number;
  deadline: string;
  status: string;
}

export interface InvestorDashboardStats {
  totalInvestments: number;
  activeDeals: number;
  portfolioValue: number;
  roi: number;
  dealsThisMonth: number;
  pendingReviews: number;
}

export interface InvestmentOpportunity {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  fundingGoal: number;
  currentFunding: number;
  valuation: number;
  roi_projection: number;
  risk_level: string;
  description: string;
  deadline: string;
}

class InvestorDashboardAPI {
  /**
   * Get investor dashboard statistics
   */
  async getDashboardStats(): Promise<InvestorDashboardStats> {
    try {
      const response = await apiClient.get('/api/roles/investor/dashboard-stats/');
      return response.data;
    } catch (error) {
      console.error('Error fetching investor dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Get investor's portfolio investments
   */
  async getPortfolioInvestments(): Promise<Investment[]> {
    try {
      const response = await apiClient.get('/api/incubator/investments/');
      
      // Transform backend data to frontend format
      return response.data.results?.map((investment: any) => ({
        id: investment.id.toString(),
        companyName: investment.business_idea?.title || 'Unknown Company',
        industry: investment.business_idea?.industry || 'Unknown',
        stage: investment.investment_type || 'Unknown',
        investmentAmount: parseFloat(investment.amount) || 0,
        currentValue: parseFloat(investment.amount) * 1.2, // Simplified calculation
        performance: Math.round(((parseFloat(investment.amount) * 1.2) / parseFloat(investment.amount) - 1) * 100),
        lastUpdate: investment.updated_at?.split('T')[0] || new Date().toISOString().split('T')[0],
        status: investment.status || 'active'
      })) || [];
    } catch (error) {
      console.error('Error fetching portfolio investments:', error);
      throw error;
    }
  }

  /**
   * Get pending investment deals
   */
  async getPendingDeals(): Promise<Deal[]> {
    try {
      const response = await apiClient.get('/api/incubator/funding-opportunities/');
      
      // Transform backend data to frontend format
      return response.data.results?.map((opportunity: any) => ({
        id: opportunity.id.toString(),
        companyName: opportunity.business_idea?.title || 'Unknown Company',
        industry: opportunity.business_idea?.industry || 'Unknown',
        stage: opportunity.funding_type || 'Unknown',
        fundingRound: opportunity.funding_type || 'Unknown',
        askAmount: parseFloat(opportunity.amount_needed) || 0,
        valuation: parseFloat(opportunity.valuation) || 0,
        deadline: opportunity.deadline || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: opportunity.status || 'reviewing'
      })) || [];
    } catch (error) {
      console.error('Error fetching pending deals:', error);
      throw error;
    }
  }

  /**
   * Get investment opportunities
   */
  async getInvestmentOpportunities(): Promise<InvestmentOpportunity[]> {
    try {
      const response = await apiClient.get('/api/incubator/funding-opportunities/?status=active');
      
      // Transform backend data to frontend format
      return response.data.results?.map((opportunity: any) => ({
        id: opportunity.id.toString(),
        companyName: opportunity.business_idea?.title || 'Unknown Company',
        industry: opportunity.business_idea?.industry || 'Technology',
        stage: opportunity.funding_type || 'Seed',
        fundingGoal: parseFloat(opportunity.amount_needed) || 0,
        currentFunding: parseFloat(opportunity.amount_raised) || 0,
        valuation: parseFloat(opportunity.valuation) || 0,
        roi_projection: Math.floor(Math.random() * 30) + 15, // Simplified calculation
        risk_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        description: opportunity.description || 'Investment opportunity',
        deadline: opportunity.deadline || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })) || [];
    } catch (error) {
      console.error('Error fetching investment opportunities:', error);
      throw error;
    }
  }

  /**
   * Get investment analytics
   */
  async getInvestmentAnalytics(): Promise<any> {
    try {
      const response = await apiClient.get('/api/roles/investor/analytics/');
      return response.data;
    } catch (error) {
      console.error('Error fetching investment analytics:', error);
      throw error;
    }
  }

  /**
   * Get market analysis data
   */
  async getMarketAnalysis(): Promise<any> {
    try {
      const response = await apiClient.get('/api/roles/investor/market-analysis/');
      return response.data;
    } catch (error) {
      console.error('Error fetching market analysis:', error);
      throw error;
    }
  }
}

export const investorDashboardAPI = new InvestorDashboardAPI();
