/**
 * Business Plan Data Synchronization Service
 * Handles synchronization between Redux state, React Query cache, and backend data
 */

import { store } from '../store';
import { 
  syncBusinessPlanFromBackend, 
  syncSectionFromBackend, 
  invalidateCache 
} from '../store/businessPlansSlice';
import { businessPlansAPI, businessPlanSectionsAPI } from './businessPlanApi';
import { queryClient } from '../lib/react-query';

export interface SyncOptions {
  forceRefresh?: boolean;
  invalidateQueries?: boolean;
  updateRedux?: boolean;
}

/**
 * Business Plan Synchronization Service
 */
class BusinessPlanSyncService {
  private syncInProgress = new Set<string>();
  private lastSyncTimes = new Map<string, number>();
  private readonly SYNC_DEBOUNCE_MS = 1000; // 1 second debounce

  /**
   * Sync a business plan from backend to all state stores
   */
  async syncBusinessPlan(
    planId: number, 
    options: SyncOptions = {}
  ): Promise<void> {
    const syncKey = `plan-${planId}`;
    
    // Prevent duplicate syncs
    if (this.syncInProgress.has(syncKey)) {
      return;
    }

    // Debounce rapid sync requests
    const lastSync = this.lastSyncTimes.get(syncKey) || 0;
    const now = Date.now();
    if (now - lastSync < this.SYNC_DEBOUNCE_MS && !options.forceRefresh) {
      return;
    }

    this.syncInProgress.add(syncKey);
    this.lastSyncTimes.set(syncKey, now);

    try {
      // Fetch latest data from backend
      const updatedPlan = await businessPlansAPI.getPlan(planId);

      // Update Redux state
      if (options.updateRedux !== false) {
        store.dispatch(syncBusinessPlanFromBackend(updatedPlan));
      }

      // Invalidate React Query cache
      if (options.invalidateQueries !== false) {
        await queryClient.invalidateQueries({
          queryKey: ['businessPlan', planId]
        });
        await queryClient.invalidateQueries({
          queryKey: ['businessPlans']
        });
      }

      console.log(`✅ Synced business plan ${planId}`);
    } catch (error) {
      console.error(`❌ Failed to sync business plan ${planId}:`, error);
      throw error;
    } finally {
      this.syncInProgress.delete(syncKey);
    }
  }

  /**
   * Sync a business plan section from backend to all state stores
   */
  async syncBusinessPlanSection(
    sectionId: number, 
    options: SyncOptions = {}
  ): Promise<void> {
    const syncKey = `section-${sectionId}`;
    
    // Prevent duplicate syncs
    if (this.syncInProgress.has(syncKey)) {
      return;
    }

    // Debounce rapid sync requests
    const lastSync = this.lastSyncTimes.get(syncKey) || 0;
    const now = Date.now();
    if (now - lastSync < this.SYNC_DEBOUNCE_MS && !options.forceRefresh) {
      return;
    }

    this.syncInProgress.add(syncKey);
    this.lastSyncTimes.set(syncKey, now);

    try {
      // Fetch latest section data from backend
      const updatedSection = await businessPlanSectionsAPI.getSection(sectionId);

      // Update Redux state
      if (options.updateRedux !== false) {
        store.dispatch(syncSectionFromBackend(updatedSection));
      }

      // Invalidate React Query cache
      if (options.invalidateQueries !== false) {
        await queryClient.invalidateQueries({
          queryKey: ['businessPlanSection', sectionId]
        });
        await queryClient.invalidateQueries({
          queryKey: ['businessPlanSections']
        });
      }

      console.log(`✅ Synced business plan section ${sectionId}`);
    } catch (error) {
      console.error(`❌ Failed to sync business plan section ${sectionId}:`, error);
      throw error;
    } finally {
      this.syncInProgress.delete(syncKey);
    }
  }

  /**
   * Sync all sections for a business plan
   */
  async syncBusinessPlanSections(
    planId: number, 
    options: SyncOptions = {}
  ): Promise<void> {
    const syncKey = `sections-${planId}`;
    
    if (this.syncInProgress.has(syncKey)) {
      return;
    }

    this.syncInProgress.add(syncKey);

    try {
      // Fetch all sections for the plan
      const sections = await businessPlanSectionsAPI.getSections(planId);

      // Sync each section individually
      const syncPromises = sections.map(section => 
        this.syncBusinessPlanSection(section.id, { 
          ...options, 
          invalidateQueries: false // We'll invalidate once at the end
        })
      );

      await Promise.all(syncPromises);

      // Invalidate queries once for all sections
      if (options.invalidateQueries !== false) {
        await queryClient.invalidateQueries({
          queryKey: ['businessPlanSections', planId]
        });
      }

      console.log(`✅ Synced all sections for business plan ${planId}`);
    } catch (error) {
      console.error(`❌ Failed to sync sections for business plan ${planId}:`, error);
      throw error;
    } finally {
      this.syncInProgress.delete(syncKey);
    }
  }

  /**
   * Invalidate all caches and force refresh
   */
  async invalidateAllCaches(): Promise<void> {
    // Clear Redux cache markers
    store.dispatch(invalidateCache());

    // Invalidate all React Query caches
    await queryClient.invalidateQueries({
      queryKey: ['businessPlans']
    });
    await queryClient.invalidateQueries({
      queryKey: ['businessPlan']
    });
    await queryClient.invalidateQueries({
      queryKey: ['businessPlanSections']
    });
    await queryClient.invalidateQueries({
      queryKey: ['businessPlanSection']
    });

    console.log('✅ Invalidated all business plan caches');
  }

  /**
   * Handle auto-save conflicts by checking for backend changes
   */
  async handleAutoSaveConflict(
    sectionId: number, 
    localContent: string
  ): Promise<{ hasConflict: boolean; backendContent?: string }> {
    try {
      const backendSection = await businessPlanSectionsAPI.getSection(sectionId);
      const backendContent = backendSection.content || '';

      // Simple conflict detection - in a real app you might want more sophisticated diff
      const hasConflict = backendContent !== localContent && 
                         backendContent.trim() !== '' && 
                         localContent.trim() !== '';

      return {
        hasConflict,
        backendContent: hasConflict ? backendContent : undefined
      };
    } catch (error) {
      console.error('Error checking for auto-save conflicts:', error);
      return { hasConflict: false };
    }
  }

  /**
   * Get sync status for debugging
   */
  getSyncStatus(): {
    inProgress: string[];
    lastSyncTimes: Record<string, number>;
  } {
    return {
      inProgress: Array.from(this.syncInProgress),
      lastSyncTimes: Object.fromEntries(this.lastSyncTimes)
    };
  }

  /**
   * Clear all sync state (useful for testing)
   */
  clearSyncState(): void {
    this.syncInProgress.clear();
    this.lastSyncTimes.clear();
  }
}

// Export singleton instance
export const businessPlanSyncService = new BusinessPlanSyncService();

// Export class for testing
export { BusinessPlanSyncService };
