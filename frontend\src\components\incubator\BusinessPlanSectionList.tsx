import React from 'react';
import { ChevronDown, ChevronUp, Check } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessPlanSection } from '../../services/businessPlanApi';

interface BusinessPlanSectionListProps {
  sections: BusinessPlanSection[];
  activeSectionId: number | null;
  onSectionSelect: (sectionId: number) => void;
  completedSections: number[];
  hasUnsavedChanges: boolean;
}

/**
 * Business Plan Section List Component
 * Displays the list of sections in a business plan with completion status
 */
const BusinessPlanSectionList: React.FC<BusinessPlanSectionListProps> = ({
  sections,
  activeSectionId,
  onSectionSelect,
  completedSections,
  hasUnsavedChanges
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isCollapsed, setIsCollapsed] = React.useState(false);

  // Calculate completion percentage
  const completionPercentage = sections.length > 0
    ? Math.round((completedSections.length / sections.length) * 100)
    : 0;

  // Toggle section list collapse
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className="business-plan-sections glass-light rounded-md overflow-hidden shadow-sm mb-6">
      {/* Section list header */}
      <div className="flex justify-between items-center p-4 border-b border-glass-border">
        <div>
          <h3 className="text-lg font-medium text-glass-primary">
            {t("businessPlan.sections")}
          </h3>
          <div className="text-sm text-glass-secondary">
            {completedSections.length} {t("businessPlan.of")} {sections.length} {t("businessPlan.sectionsComplete")}
          </div>
        </div>
        <div className="flex items-center">
          {/* Progress indicator */}
          <div className="mr-4">
            <div className="flex items-center">
              <div className="w-16 h-2 bg-glass-border rounded-full overflow-hidden">
                <div
                  className="h-full bg-purple-500"
                  style={{ width: `${completionPercentage}%` }}
                ></div>
              </div>
              <span className="ml-2 text-sm text-glass-secondary">
                {completionPercentage}%
              </span>
            </div>
          </div>
          {/* Collapse toggle button */}
          <button
            onClick={toggleCollapse}
            className="p-1 rounded-full hover:bg-glass-hover transition-colors"
            aria-label={isCollapsed ? t("common.expand") : t("common.collapse")}
          >
            {isCollapsed ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
          </button>
        </div>
      </div>

      {/* Section list */}
      {!isCollapsed && (
        <div className="p-2">
          <ul className="space-y-1">
            {sections.map((section) => {
              const isActive = section.id === activeSectionId;
              const isCompleted = completedSections.includes(section.id);

              return (
                <li key={section.id}>
                  <button
                    onClick={() => onSectionSelect(section.id)}
                    className={`w-full text-left p-3 rounded-md flex items-center justify-between transition-colors ${
                      isActive
                        ? "bg-purple-500/20 text-white"
                        : "hover:bg-glass-hover text-glass-primary"
                    }`}
                    disabled={isActive && hasUnsavedChanges}
                    aria-current={isActive ? "true" : "false"}
                  >
                    <div className="flex items-center">
                      {/* Completion indicator */}
                      <div
                        className={`w-5 h-5 rounded-full flex items-center justify-center mr-3 ${
                          isCompleted
                            ? "bg-green-500 text-white"
                            : "bg-glass-border text-glass-secondary"
                        }`}
                      >
                        {isCompleted && <Check size={14} />}
                      </div>
                      {/* Section title */}
                      <span className={isCompleted ? "text-glass-primary" : ""}>
                        {section.title}
                      </span>
                    </div>
                    {/* Warning indicator for unsaved changes */}
                    {isActive && hasUnsavedChanges && (
                      <span className="text-amber-400 text-xs">
                        {t("businessPlan.unsavedChanges")}
                      </span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

export default BusinessPlanSectionList;