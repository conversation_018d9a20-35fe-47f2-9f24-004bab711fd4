/**
 * Comprehensive Error Handling Utilities
 * Provides consistent error handling patterns across the application
 */

export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
  timestamp: Date;
  userFriendly: boolean;
}

export class ErrorHandler {
  /**
   * Create a user-friendly error from any error type
   */
  static createAppError(error: any, context?: string): AppError {
    const timestamp = new Date();
    
    // Handle API errors
    if (error?.status && error?.message) {
      return {
        message: this.getApiErrorMessage(error.status, error.message),
        code: `API_${error.status}`,
        status: error.status,
        details: error,
        timestamp,
        userFriendly: true
      };
    }
    
    // Handle network errors
    if (!navigator.onLine) {
      return {
        message: 'You appear to be offline. Please check your internet connection.',
        code: 'NETWORK_OFFLINE',
        timestamp,
        userFriendly: true
      };
    }
    
    // Handle JavaScript errors
    if (error instanceof Error) {
      return {
        message: this.getJavaScriptErrorMessage(error, context),
        code: 'J<PERSON>_ERROR',
        details: {
          name: error.name,
          stack: error.stack,
          context
        },
        timestamp,
        userFriendly: true
      };
    }
    
    // Handle unknown errors
    return {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNKNOWN_ERROR',
      details: error,
      timestamp,
      userFriendly: true
    };
  }

  /**
   * Get user-friendly message for API errors
   */
  private static getApiErrorMessage(status: number, originalMessage: string): string {
    switch (status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'You need to log in to access this feature.';
      case 403:
        return 'You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 409:
        return 'This action conflicts with existing data.';
      case 422:
        return 'Please check your input data and try again.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Our team has been notified.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return originalMessage || 'An error occurred while processing your request.';
    }
  }

  /**
   * Get user-friendly message for JavaScript errors
   */
  private static getJavaScriptErrorMessage(error: Error, context?: string): string {
    // Handle specific error types
    if (error.name === 'TypeError') {
      return 'A technical error occurred. Please refresh the page and try again.';
    }
    
    if (error.name === 'ReferenceError') {
      return 'A component failed to load properly. Please refresh the page.';
    }
    
    if (error.message.includes('fetch')) {
      return 'Failed to connect to the server. Please check your internet connection.';
    }
    
    if (error.message.includes('JSON')) {
      return 'Received invalid data from the server. Please try again.';
    }
    
    // Context-specific messages
    if (context) {
      return `An error occurred in ${context}. Please try again or contact support if the problem persists.`;
    }
    
    return 'An unexpected error occurred. Please refresh the page and try again.';
  }

  /**
   * Log error for debugging (development only)
   */
  static logError(error: AppError, component?: string): void {
    if (import.meta.env.DEV) {
      console.group(`🚨 Error in ${component || 'Application'}`);
      console.error('Message:', error.message);
      console.error('Code:', error.code);
      console.error('Status:', error.status);
      console.error('Timestamp:', error.timestamp);
      console.error('Details:', error.details);
      console.groupEnd();
    }
  }

  /**
   * Handle async operations with consistent error handling
   */
  static async handleAsync<T>(
    operation: () => Promise<T>,
    context?: string,
    fallback?: T
  ): Promise<{ data?: T; error?: AppError }> {
    try {
      const data = await operation();
      return { data };
    } catch (error) {
      const appError = this.createAppError(error, context);
      this.logError(appError, context);
      
      if (fallback !== undefined) {
        return { data: fallback, error: appError };
      }
      
      return { error: appError };
    }
  }

  /**
   * Retry operation with exponential backoff
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}

/**
 * React hook for error handling with toast integration
 */
export function useErrorHandler() {
  const handleError = (error: any, context?: string, showToast: boolean = true) => {
    const appError = ErrorHandler.createAppError(error, context);
    ErrorHandler.logError(appError, context);

    // Show toast notification if requested
    if (showToast && appError.userFriendly) {
      // Dispatch custom event for toast system
      window.dispatchEvent(new CustomEvent('toast', {
        detail: {
          type: 'error',
          message: appError.message
        }
      }));
    }

    return appError;
  };

  const handleAsync = async <T>(
    operation: () => Promise<T>,
    context?: string,
    fallback?: T,
    showToast: boolean = true
  ) => {
    const result = await ErrorHandler.handleAsync(operation, context, fallback);

    // Show toast for errors
    if (result.error && showToast && result.error.userFriendly) {
      window.dispatchEvent(new CustomEvent('toast', {
        detail: {
          type: 'error',
          message: result.error.message
        }
      }));
    }

    return result;
  };

  const showSuccess = (message: string) => {
    window.dispatchEvent(new CustomEvent('toast', {
      detail: {
        type: 'success',
        message
      }
    }));
  };

  const showWarning = (message: string) => {
    window.dispatchEvent(new CustomEvent('toast', {
      detail: {
        type: 'warning',
        message
      }
    }));
  };

  const showInfo = (message: string) => {
    window.dispatchEvent(new CustomEvent('toast', {
      detail: {
        type: 'info',
        message
      }
    }));
  };

  return {
    handleError,
    handleAsync,
    showSuccess,
    showWarning,
    showInfo
  };
}
