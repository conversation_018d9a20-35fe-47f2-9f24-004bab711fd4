/**
 * Quick Authentication Test
 * Simple test to verify authentication is working
 */

import { getAuthToken, getRefreshToken, authAPI } from '../services/api';
import { businessIdeasAPI } from '../services/incubatorApi';
import { centralizedAiApi } from '../services/centralizedAiApi';

export async function runQuickAuthTest(): Promise<void> {
  console.log('🧪 Running Quick Authentication Test...');
  console.log('=====================================');

  // Test 1: Check token storage
  const token = getAuthToken();
  const refreshToken = getRefreshToken();
  
  console.log('📝 Token Status:');
  console.log(`  Access Token: ${token ? '✅ Present' : '❌ Missing'}`);
  console.log(`  Refresh Token: ${refreshToken ? '✅ Present' : '❌ Missing'}`);
  
  if (!token) {
    console.log('⚠️ No access token found. Please log in first.');
    return;
  }

  // Test 2: Parse token
  try {
    const parts = token.split('.');
    if (parts.length === 3) {
      const payload = JSON.parse(atob(parts[1]));
      const now = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < now;
      
      console.log('🔍 Token Analysis:');
      console.log(`  User ID: ${payload.user_id}`);
      console.log(`  Expires: ${new Date(payload.exp * 1000).toLocaleString()}`);
      console.log(`  Status: ${isExpired ? '❌ Expired' : '✅ Valid'}`);
      
      if (isExpired) {
        console.log('⚠️ Token is expired. Testing refresh...');
        const refreshSuccess = await authAPI.refreshToken();
        console.log(`  Refresh Result: ${refreshSuccess ? '✅ Success' : '❌ Failed'}`);
      }
    }
  } catch (error) {
    console.log('❌ Error parsing token:', error);
  }

  // Test 3: Test current user endpoint
  console.log('👤 Testing Current User API:');
  try {
    const user = await authAPI.getCurrentUser();
    console.log(`  ✅ Success: ${user.username} (${user.role})`);
  } catch (error: any) {
    console.log(`  ❌ Failed: ${error?.message || error}`);
    if (error?.status === 401) {
      console.log('  🔄 Attempting token refresh...');
      try {
        const refreshSuccess = await authAPI.refreshToken();
        if (refreshSuccess) {
          const user = await authAPI.getCurrentUser();
          console.log(`  ✅ After refresh: ${user.username} (${user.role})`);
        } else {
          console.log('  ❌ Refresh failed');
        }
      } catch (refreshError) {
        console.log(`  ❌ Refresh error: ${refreshError}`);
      }
    }
  }

  // Test 4: Test Business Ideas API
  console.log('💡 Testing Business Ideas API:');
  try {
    const ideas = await businessIdeasAPI.getBusinessIdeas();
    console.log(`  ✅ Success: Retrieved ${ideas.length} business ideas`);
  } catch (error: any) {
    console.log(`  ❌ Failed: ${error?.message || error}`);
  }

  // Test 5: Test AI Chat API
  console.log('🤖 Testing AI Chat API:');
  try {
    const response = await centralizedAiApi.chat('Test authentication message');
    console.log(`  ✅ Success: AI responded`);
  } catch (error: any) {
    console.log(`  ❌ Failed: ${error?.message || error}`);
  }

  console.log('=====================================');
  console.log('🧪 Quick Authentication Test Complete');
}

// Auto-run in development after a delay
if (process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    const token = getAuthToken();
    if (token) {
      runQuickAuthTest();
    } else {
      console.log('🔐 No token found, skipping auth test. Log in to run tests.');
    }
  }, 3000);
}

// Add to window for manual testing
if (typeof window !== 'undefined') {
  (window as any).testAuth = runQuickAuthTest;
  console.log('🧪 Auth test loaded. Use testAuth() in console to run manually.');
}

export default runQuickAuthTest;
