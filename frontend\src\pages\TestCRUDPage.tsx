import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import {
  BusinessPlanCreateForm,
  BusinessPlanList,
  BusinessPlanEditor,
  BusinessPlanDeleteModal
} from '../components/business-plans';
import { BusinessPlan } from '../services/businessPlanApi';

const TestCRUDPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // State for testing different components
  const [activeComponent, setActiveComponent] = useState<'list' | 'create' | 'edit' | 'delete'>('list');
  const [selectedPlan, setSelectedPlan] = useState<BusinessPlan | null>(null);

  // Mock business plan for testing
  const mockBusinessPlan: BusinessPlan = {
    id: 1,
    title: "خطة أعمال تطبيق التجارة الإلكترونية",
    business_idea: 1,
    business_idea_title: "منصة التجارة الإلكترونية للمنتجات المحلية",
    template: 1,
    template_name: "خطة الأعمال القياسية",
    status: "draft",
    completion_percentage: 65,
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-01-20T14:45:00Z",
    user: 1
  };

  const handleCreateSuccess = (businessPlan: BusinessPlan) => {
    console.log('Business plan created:', businessPlan);
    setActiveComponent('list');
  };

  const handleEdit = (plan: BusinessPlan) => {
    setSelectedPlan(plan);
    setActiveComponent('edit');
  };

  const handleView = (plan: BusinessPlan) => {
    setSelectedPlan(plan);
    setActiveComponent('edit');
  };

  const handleDelete = (plan: BusinessPlan) => {
    setSelectedPlan(plan);
    setActiveComponent('delete');
  };

  const handleDeleteSuccess = () => {
    console.log('Business plan deleted');
    setSelectedPlan(null);
    setActiveComponent('list');
  };

  const renderComponent = () => {
    switch (activeComponent) {
      case 'create':
        return (
          <BusinessPlanCreateForm
            onSuccess={handleCreateSuccess}
            onCancel={() => setActiveComponent('list')}
          />
        );
      
      case 'edit':
        return selectedPlan ? (
          <BusinessPlanEditor
            businessPlanId={selectedPlan.id}
            onBack={() => setActiveComponent('list')}
          />
        ) : null;
      
      case 'delete':
        return selectedPlan ? (
          <BusinessPlanDeleteModal
            businessPlan={selectedPlan}
            isOpen={true}
            onClose={() => setActiveComponent('list')}
            onSuccess={handleDeleteSuccess}
          />
        ) : null;
      
      case 'list':
      default:
        return (
          <BusinessPlanList
            onCreateNew={() => setActiveComponent('create')}
            onEdit={handleEdit}
            onView={handleView}
            onDelete={handleDelete}
          />
        );
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-7xl mx-auto w-full">
          {/* Header */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 mb-6">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <h1 className="text-2xl font-bold text-white mb-2">
                  Business Plan CRUD Testing
                </h1>
                <p className="text-gray-300">
                  Comprehensive testing of all CRUD operations
                </p>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className={`flex gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={() => setActiveComponent('list')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeComponent === 'list'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                📋 List View
              </button>
              <button
                onClick={() => setActiveComponent('create')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeComponent === 'create'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                ➕ Create Form
              </button>
              <button
                onClick={() => {
                  setSelectedPlan(mockBusinessPlan);
                  setActiveComponent('edit');
                }}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeComponent === 'edit'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                ✏️ Editor
              </button>
              <button
                onClick={() => {
                  setSelectedPlan(mockBusinessPlan);
                  setActiveComponent('delete');
                }}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeComponent === 'delete'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                🗑️ Delete Modal
              </button>
            </div>
          </div>

          {/* Component Display */}
          <div className="space-y-6">
            {renderComponent()}
          </div>

          {/* Testing Information */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 mt-6">
            <h3 className="text-lg font-semibold text-white mb-4">
              🧪 Testing Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
              <div>
                <h4 className="font-medium text-white mb-2">✅ Features Tested:</h4>
                <ul className="space-y-1">
                  <li>• Business Plan List with search/filter</li>
                  <li>• Template-based creation wizard</li>
                  <li>• Section-based editor with auto-save</li>
                  <li>• Safe deletion with confirmation</li>
                  <li>• Arabic/RTL support throughout</li>
                  <li>• Glass Morphism design system</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-white mb-2">🎯 Current Component:</h4>
                <p className="capitalize">{activeComponent.replace('_', ' ')}</p>
                <h4 className="font-medium text-white mb-2 mt-4">📊 Mock Data:</h4>
                <p>Using sample business plan data for testing</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCRUDPage;
