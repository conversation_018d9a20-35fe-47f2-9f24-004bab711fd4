import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from './store';
import AppRoutes from './routes';
import AuthInitializer from './components/AuthInitializer';
import { AuthenticationGuard, AuthDebugInfo } from './components/auth/AuthenticationGuard';
import LanguageProvider from './components/LanguageProvider';
import QueryProvider from './providers/QueryProvider';
import ToastProvider from './components/ui/Toast';
import { AIContextProvider } from './contexts/AIContextProvider';
import ErrorBoundary from './components/ErrorBoundary';
import DebugWrapper from './components/debug/DebugWrapper';
import SessionManager from './components/SessionManager';
import TranslationValidator from './components/dev/TranslationValidator';
// import './utils/testRunner'; // Load role access control tests - file not found
import './utils/authTestRunner'; // Load authentication tests
import './utils/authDebugger'; // Load authentication debugger
import { initAnalytics, trackError } from './utils/analytics';
import { register as registerServiceWorker } from './utils/serviceWorkerRegistration';


import './index.css';
import './styles/rtl-consolidated.css';
import './i18n/index'; // Import i18n configuration



// Glass morphism styling applied via Tailwind CSS



// Initialize performance analytics
initAnalytics({
  sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 10% of users in production, all in development
  apiEndpoint: '/api/analytics/performance',
});


// Global error handler for unhandled errors
window.addEventListener('error', (event) => {
  if (event.error?.message?.includes('current_role')) {
    // Log error in development only
    if (import.meta.env.DEV) {
      console.error('GLOBAL ERROR HANDLER: current_role error detected:', {
        message: event.error.message,
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    }

    // Prevent the error from crashing the app
    event.preventDefault();

    // Show a user-friendly message in development only
    if (import.meta.env.DEV) {
      console.warn('A role-related error was caught and handled. The application should continue to work.');
    }
  }
});

// Global promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason?.message?.includes('current_role')) {
    // Log error in development only
    if (import.meta.env.DEV) {
      console.error('GLOBAL PROMISE REJECTION: current_role error detected:', {
        reason: event.reason,
        promise: event.promise
      });
    }

    // Prevent the error from crashing the app
    event.preventDefault();

    if (import.meta.env.DEV) {
      console.warn('A role-related promise rejection was caught and handled.');
    }
  }
});

// Register service worker for offline capabilities
registerServiceWorker({
  onSuccess: (registration) => {
    console.log('Service worker registration successful with scope:', registration.scope);
  },
  onUpdate: (registration) => {
    console.log('New content is available; please refresh.');
    // You could show a notification to the user here
  },
  onError: (error) => {
    console.error('Error during service worker registration:', error);
    trackError('ServiceWorkerRegistration', error.message);
  },
  onOffline: () => {
    console.log('Application is offline');
    // You could show an offline indicator here
  },
  onOnline: () => {
    console.log('Application is back online');
    // You could hide the offline indicator here
  }
});

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <BrowserRouter>
          <LanguageProvider>
            <QueryProvider>
              <ToastProvider>
                <AIContextProvider>
                  <AuthInitializer />
                  <AuthenticationGuard requireAuth={false}>
                    <AppRoutes />
                  </AuthenticationGuard>
                  <DebugWrapper />
                  <SessionManager />
                  <TranslationValidator />
                  <AuthDebugInfo />
                </AIContextProvider>
              </ToastProvider>
            </QueryProvider>
          </LanguageProvider>
        </BrowserRouter>
      </Provider>
    </ErrorBoundary>
  </StrictMode>
);
