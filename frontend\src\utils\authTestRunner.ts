/**
 * Authentication System Test Runner
 * Comprehensive tests for the authentication fixes
 */

import { getAuthToken, getRefreshToken, authAPI, clearAuthTokens } from '../services/api';
import { businessIdeasAPI } from '../services/incubatorApi';
import { centralizedAiApi } from '../services/centralizedAiApi';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'skip';
  message: string;
  duration: number;
}

class AuthTestRunner {
  private results: TestResult[] = [];

  /**
   * Run all authentication tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Authentication System Tests...');
    this.results = [];

    await this.testTokenStorage();
    await this.testTokenConsistency();
    await this.testApiAuthentication();
    await this.testTokenRefresh();
    await this.testBusinessIdeasAPI();
    await this.testAIChatAPI();
    await this.testErrorHandling();

    this.printResults();
    return this.results;
  }

  /**
   * Test token storage and retrieval
   */
  private async testTokenStorage(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      
      if (token && refreshToken) {
        this.addResult('Token Storage', 'pass', 'Tokens found in localStorage', startTime);
      } else if (!token && !refreshToken) {
        this.addResult('Token Storage', 'skip', 'No tokens found (not logged in)', startTime);
      } else {
        this.addResult('Token Storage', 'fail', 'Inconsistent token state', startTime);
      }
    } catch (error) {
      this.addResult('Token Storage', 'fail', `Error: ${error}`, startTime);
    }
  }

  /**
   * Test token key consistency across services
   */
  private async testTokenConsistency(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      if (!token) {
        this.addResult('Token Consistency', 'skip', 'No token to test', startTime);
        return;
      }

      // Check if token is properly formatted JWT
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.addResult('Token Consistency', 'fail', 'Invalid JWT format', startTime);
        return;
      }

      // Check if token payload is readable
      const payload = JSON.parse(atob(parts[1]));
      if (!payload.user_id || !payload.exp) {
        this.addResult('Token Consistency', 'fail', 'Invalid token payload', startTime);
        return;
      }

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) {
        this.addResult('Token Consistency', 'fail', 'Token is expired', startTime);
        return;
      }

      this.addResult('Token Consistency', 'pass', 'Token format and content valid', startTime);
    } catch (error) {
      this.addResult('Token Consistency', 'fail', `Error: ${error}`, startTime);
    }
  }

  /**
   * Test API authentication headers
   */
  private async testApiAuthentication(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      if (!token) {
        this.addResult('API Authentication', 'skip', 'No token to test', startTime);
        return;
      }

      // Test current user endpoint
      const user = await authAPI.getCurrentUser();
      if (user && user.id) {
        this.addResult('API Authentication', 'pass', `Authenticated as ${user.username}`, startTime);
      } else {
        this.addResult('API Authentication', 'fail', 'Failed to get current user', startTime);
      }
    } catch (error: any) {
      if (error?.status === 401) {
        this.addResult('API Authentication', 'fail', 'Authentication failed (401)', startTime);
      } else {
        this.addResult('API Authentication', 'fail', `Error: ${error?.message || error}`, startTime);
      }
    }
  }

  /**
   * Test token refresh functionality
   */
  private async testTokenRefresh(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        this.addResult('Token Refresh', 'skip', 'No refresh token to test', startTime);
        return;
      }

      const success = await authAPI.refreshToken();
      if (success) {
        this.addResult('Token Refresh', 'pass', 'Token refresh successful', startTime);
      } else {
        this.addResult('Token Refresh', 'fail', 'Token refresh failed', startTime);
      }
    } catch (error) {
      this.addResult('Token Refresh', 'fail', `Error: ${error}`, startTime);
    }
  }

  /**
   * Test Business Ideas API authentication
   */
  private async testBusinessIdeasAPI(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      if (!token) {
        this.addResult('Business Ideas API', 'skip', 'No token to test', startTime);
        return;
      }

      const ideas = await businessIdeasAPI.getBusinessIdeas();
      if (Array.isArray(ideas)) {
        this.addResult('Business Ideas API', 'pass', `Retrieved ${ideas.length} business ideas`, startTime);
      } else {
        this.addResult('Business Ideas API', 'fail', 'Invalid response format', startTime);
      }
    } catch (error: any) {
      if (error?.status === 401) {
        this.addResult('Business Ideas API', 'fail', 'Authentication failed (401)', startTime);
      } else {
        this.addResult('Business Ideas API', 'fail', `Error: ${error?.message || error}`, startTime);
      }
    }
  }

  /**
   * Test AI Chat API authentication
   */
  private async testAIChatAPI(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const token = getAuthToken();
      if (!token) {
        this.addResult('AI Chat API', 'skip', 'No token to test', startTime);
        return;
      }

      const response = await centralizedAiApi.chat('Test message for authentication');
      if (response && response.message) {
        this.addResult('AI Chat API', 'pass', 'AI chat authentication successful', startTime);
      } else {
        this.addResult('AI Chat API', 'fail', 'Invalid AI response', startTime);
      }
    } catch (error: any) {
      if (error?.status === 401) {
        this.addResult('AI Chat API', 'fail', 'Authentication failed (401)', startTime);
      } else {
        this.addResult('AI Chat API', 'fail', `Error: ${error?.message || error}`, startTime);
      }
    }
  }

  /**
   * Test error handling for authentication failures
   */
  private async testErrorHandling(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Temporarily clear tokens to test error handling
      const originalToken = getAuthToken();
      const originalRefreshToken = getRefreshToken();
      
      clearAuthTokens();
      
      try {
        await authAPI.getCurrentUser();
        this.addResult('Error Handling', 'fail', 'Should have failed without tokens', startTime);
      } catch (error: any) {
        if (error?.message?.includes('No authentication token') || error?.status === 401) {
          this.addResult('Error Handling', 'pass', 'Properly handles missing tokens', startTime);
        } else {
          this.addResult('Error Handling', 'fail', `Unexpected error: ${error?.message}`, startTime);
        }
      }
      
      // Restore original tokens
      if (originalToken && originalRefreshToken) {
        localStorage.setItem('yasmeen_auth_token', originalToken);
        localStorage.setItem('yasmeen_refresh_token', originalRefreshToken);
      }
    } catch (error) {
      this.addResult('Error Handling', 'fail', `Test error: ${error}`, startTime);
    }
  }

  /**
   * Add test result
   */
  private addResult(name: string, status: 'pass' | 'fail' | 'skip', message: string, startTime: number): void {
    const duration = Date.now() - startTime;
    this.results.push({ name, status, message, duration });
  }

  /**
   * Print test results to console
   */
  private printResults(): void {
    console.log('\n🧪 Authentication Test Results:');
    console.log('================================');
    
    let passed = 0;
    let failed = 0;
    let skipped = 0;
    
    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⏭️';
      console.log(`${icon} ${result.name}: ${result.message} (${result.duration}ms)`);
      
      if (result.status === 'pass') passed++;
      else if (result.status === 'fail') failed++;
      else skipped++;
    });
    
    console.log('================================');
    console.log(`Total: ${this.results.length} | Passed: ${passed} | Failed: ${failed} | Skipped: ${skipped}`);
    
    if (failed > 0) {
      console.log('❌ Some authentication tests failed. Check the issues above.');
    } else if (passed > 0) {
      console.log('✅ All authentication tests passed!');
    } else {
      console.log('⏭️ All tests were skipped (likely not logged in).');
    }
  }
}

// Export singleton instance
export const authTestRunner = new AuthTestRunner();

// Auto-run tests in development when this module is imported
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to allow app initialization
  setTimeout(() => {
    authTestRunner.runAllTests();
  }, 2000);
}

export default AuthTestRunner;
