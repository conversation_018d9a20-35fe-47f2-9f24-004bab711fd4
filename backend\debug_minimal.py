#!/usr/bin/env python
"""
Minimal Django debug script to isolate startup issues
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(backend_dir))

print("🔍 Django Startup Debug")
print(f"Python version: {sys.version}")
print(f"Django version: {django.get_version()}")
print(f"Backend directory: {backend_dir}")

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')

try:
    print("\n1. Loading environment variables...")
    from dotenv import load_dotenv
    load_dotenv(backend_dir / '.env')
    print("✅ Environment variables loaded")
except Exception as e:
    print(f"❌ Environment loading failed: {e}")

try:
    print("\n2. Setting up Django...")
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

try:
    print("\n3. Testing database connection...")
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
    print(f"✅ Database connection successful: {result}")
except Exception as e:
    print(f"❌ Database connection failed: {e}")

try:
    print("\n4. Testing app imports...")
    from core.models import *
    print("✅ Core models imported")
    
    from users.models import *
    print("✅ Users models imported")
    
    from incubator.models import *
    print("✅ Incubator models imported")
    
except Exception as e:
    print(f"❌ Model import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\n5. Testing basic Django commands...")
    from django.core.management import execute_from_command_line
    print("✅ Django management commands available")
except Exception as e:
    print(f"❌ Management commands failed: {e}")

print("\n🎉 Debug complete!")
print("If all tests passed, Django should work normally.")
print("If any failed, those are the areas to fix.")
