/**
 * Mock Authentication Service for Testing
 * This provides a fallback authentication system when the backend is not available
 */

import { User } from './api';

// Mock user data for testing
const MOCK_USERS: Record<string, { password: string; user: User }> = {
  'testuser': {
    password: 'testpass123',
    user: {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      is_staff: false,
      is_superuser: false,
      is_active: true,
      date_joined: '2024-01-01T00:00:00Z',
      profile: {
        id: 1,
        bio: 'Test user for CRUD testing',
        location: 'Test City',
        website: '',
        linkedin: '',
        twitter: '',
        avatar: null,
        phone: '',
        date_of_birth: null,
        gender: 'other',
        preferred_language: 'ar',
        timezone: 'UTC',
        notification_preferences: {
          email_notifications: true,
          push_notifications: true,
          sms_notifications: false
        },
        privacy_settings: {
          profile_visibility: 'public',
          show_email: false,
          show_phone: false
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    }
  },
  'ahmed_test_user_2024': {
    password: 'TestPassword123!',
    user: {
      id: 2,
      username: 'ahmed_test_user_2024',
      email: '<EMAIL>',
      first_name: 'Ahmed',
      last_name: 'Test',
      is_staff: false,
      is_superuser: false,
      is_active: true,
      date_joined: '2024-01-01T00:00:00Z',
      profile: {
        id: 2,
        user: 2,
        bio: 'Ahmed test user for business plan testing',
        location: 'Damascus, Syria',
        website: '',
        linkedin: '',
        twitter: '',
        avatar: null,
        phone: '',
        date_of_birth: null,
        gender: 'male',
        preferred_language: 'ar',
        timezone: 'Asia/Damascus',
        notification_preferences: {
          email_notifications: true,
          push_notifications: true,
          sms_notifications: false
        },
        privacy_settings: {
          profile_visibility: 'public',
          show_email: false,
          show_phone: false
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    }
  }
};

// Use the same token keys as the main application
const MOCK_TOKEN_KEY = 'yasmeen_auth_token';
const MOCK_REFRESH_TOKEN_KEY = 'yasmeen_refresh_token';

export class MockAuthService {
  static isEnabled(): boolean {
    // Enable mock auth when backend is not available or explicitly enabled
    const envEnabled = import.meta.env.VITE_MOCK_AUTH === 'true';
    const localEnabled = localStorage.getItem('enable_mock_auth') === 'true';
    const hasToken = !!localStorage.getItem(MOCK_TOKEN_KEY);

    console.log('🧪 MockAuth: isEnabled check:', {
      envEnabled,
      localEnabled,
      hasToken,
      result: envEnabled || localEnabled || hasToken
    });

    return envEnabled || localEnabled || hasToken;
  }

  static enableMockAuth(): void {
    localStorage.setItem('enable_mock_auth', 'true');
    console.log('🧪 Mock authentication enabled for testing');
  }

  static disableMockAuth(): void {
    localStorage.removeItem('enable_mock_auth');
    localStorage.removeItem(MOCK_TOKEN_KEY);
    localStorage.removeItem(MOCK_REFRESH_TOKEN_KEY);
    console.log('🧪 Mock authentication disabled');
  }

  static async login(username: string, password: string): Promise<User> {
    console.log('🧪 MockAuth: Attempting login for:', username);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockUser = MOCK_USERS[username];
    if (!mockUser || mockUser.password !== password) {
      throw new Error('Invalid username or password');
    }

    // Generate mock JWT-like tokens
    const now = Math.floor(Date.now() / 1000);
    const exp = now + (30 * 60); // 30 minutes from now

    const mockPayload = {
      user_id: mockUser.user.id,
      username: username,
      exp: exp,
      iat: now
    };

    // Create a mock JWT (header.payload.signature format)
    const mockHeader = btoa(JSON.stringify({ typ: 'JWT', alg: 'HS256' }));
    const mockPayloadEncoded = btoa(JSON.stringify(mockPayload));
    const mockSignature = btoa(`mock_signature_${username}_${now}`);
    const mockToken = `${mockHeader}.${mockPayloadEncoded}.${mockSignature}`;

    const mockRefreshToken = `mock_refresh_${Date.now()}_${username}`;

    // Store mock tokens using the correct keys
    localStorage.setItem(MOCK_TOKEN_KEY, mockToken);
    localStorage.setItem(MOCK_REFRESH_TOKEN_KEY, mockRefreshToken);

    console.log('🧪 MockAuth: Tokens stored with keys:', {
      tokenKey: MOCK_TOKEN_KEY,
      refreshKey: MOCK_REFRESH_TOKEN_KEY,
      hasToken: !!localStorage.getItem(MOCK_TOKEN_KEY),
      hasRefresh: !!localStorage.getItem(MOCK_REFRESH_TOKEN_KEY)
    });

    console.log('🧪 MockAuth: Login successful for:', username);
    return mockUser.user;
  }

  static async register(userData: {
    username: string;
    email: string;
    password: string;
    password_confirm: string;
    first_name?: string;
    last_name?: string;
  }): Promise<User> {
    console.log('🧪 MockAuth: Attempting registration for:', userData.username);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Check if user already exists
    if (MOCK_USERS[userData.username]) {
      throw new Error('Username already exists');
    }

    // Validate password confirmation
    if (userData.password !== userData.password_confirm) {
      throw new Error('Passwords do not match');
    }

    // Create new mock user
    const newUser: User = {
      id: Object.keys(MOCK_USERS).length + 1,
      username: userData.username,
      email: userData.email,
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      is_staff: false,
      is_superuser: false,
      is_active: true,
      date_joined: new Date().toISOString(),
      profile: {
        id: Object.keys(MOCK_USERS).length + 1,
        user: Object.keys(MOCK_USERS).length + 1,
        bio: '',
        location: '',
        website: '',
        linkedin: '',
        twitter: '',
        avatar: null,
        phone: '',
        date_of_birth: null,
        gender: 'other',
        preferred_language: 'ar',
        timezone: 'UTC',
        notification_preferences: {
          email_notifications: true,
          push_notifications: true,
          sms_notifications: false
        },
        privacy_settings: {
          profile_visibility: 'public',
          show_email: false,
          show_phone: false
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    // Add to mock users
    MOCK_USERS[userData.username] = {
      password: userData.password,
      user: newUser
    };

    // Generate mock tokens
    const mockToken = `mock_token_${Date.now()}_${userData.username}`;
    const mockRefreshToken = `mock_refresh_${Date.now()}_${userData.username}`;

    // Store mock tokens
    localStorage.setItem(MOCK_TOKEN_KEY, mockToken);
    localStorage.setItem(MOCK_REFRESH_TOKEN_KEY, mockRefreshToken);

    console.log('🧪 MockAuth: Registration successful for:', userData.username);
    return newUser;
  }

  static async getCurrentUser(): Promise<User> {
    console.log('🧪 MockAuth: getCurrentUser called');

    const token = localStorage.getItem(MOCK_TOKEN_KEY);
    console.log('🧪 MockAuth: Token from storage:', token ? 'Present' : 'Missing');

    if (!token) {
      console.log('🧪 MockAuth: No token found in localStorage with key:', MOCK_TOKEN_KEY);
      console.log('🧪 MockAuth: Available localStorage keys:', Object.keys(localStorage));
      throw new Error('No authentication token');
    }

    try {
      // Parse JWT-like token to extract user info
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.error('🧪 MockAuth: Invalid token format, parts:', parts.length);
        throw new Error('Invalid token format');
      }

      const payload = JSON.parse(atob(parts[1]));
      console.log('🧪 MockAuth: Token payload:', payload);

      const username = payload.username;
      const mockUser = MOCK_USERS[username];

      if (!mockUser) {
        console.error('🧪 MockAuth: User not found for username:', username);
        console.log('🧪 MockAuth: Available users:', Object.keys(MOCK_USERS));
        throw new Error('Invalid token - user not found');
      }

      // Check token expiration
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) {
        console.error('🧪 MockAuth: Token expired. Now:', now, 'Exp:', payload.exp);
        throw new Error('Token expired');
      }

      console.log('🧪 MockAuth: Current user retrieved successfully:', mockUser.user.username);
      return mockUser.user;
    } catch (error) {
      console.error('🧪 MockAuth: getCurrentUser failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Invalid token: ${errorMessage}`);
    }
  }

  static async logout(): Promise<{ message: string }> {
    localStorage.removeItem(MOCK_TOKEN_KEY);
    localStorage.removeItem(MOCK_REFRESH_TOKEN_KEY);
    console.log('🧪 MockAuth: Logout successful');
    return { message: "Logged out successfully" };
  }

  static hasValidToken(): boolean {
    return !!localStorage.getItem(MOCK_TOKEN_KEY);
  }

  static getToken(): string | null {
    return localStorage.getItem(MOCK_TOKEN_KEY);
  }
}
