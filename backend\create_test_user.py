#!/usr/bin/env python
"""
Create a test user for end-to-end testing
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings_minimal')

# Setup Django
django.setup()

from django.contrib.auth.models import User

def create_test_user():
    """Create a test user for authentication testing"""
    try:
        # Delete existing test user if exists
        try:
            existing_user = User.objects.get(username='testuser')
            existing_user.delete()
            print("Deleted existing test user")
        except User.DoesNotExist:
            pass

        # Create new test user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        print(f"✅ Created test user: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Password: testpass123")
        print(f"   Full name: {user.first_name} {user.last_name}")
        
        return user
    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return None

if __name__ == '__main__':
    print("🚀 Creating test user for end-to-end testing...\n")
    user = create_test_user()
    
    if user:
        print("\n✅ Test user created successfully!")
        print("\nYou can now use these credentials to log in:")
        print("Username: testuser")
        print("Password: testpass123")
    else:
        print("\n❌ Failed to create test user")
