import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  FileText,
  Plus,
  ArrowRight,
  ArrowLeft,
  Check,
  AlertCircle,
  Sparkles,
  Search,
  Filter,
  Star,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react';
import {
  BusinessPlanTemplate,
  BusinessPlan,
  businessPlanTemplatesAPI,
  businessPlansAPI
} from '../../services/businessPlanApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';

interface BusinessPlanCreateFormProps {
  businessIdeaId?: number;
  onSuccess?: (businessPlan: BusinessPlan) => void;
  onCancel?: () => void;
}

const BusinessPlanCreateForm: React.FC<BusinessPlanCreateFormProps> = ({
  businessIdeaId,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // State management
  const [step, setStep] = useState<'template' | 'details' | 'creating'>('template');
  const [templates, setTemplates] = useState<BusinessPlanTemplate[]>([]);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<BusinessPlanTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    title: '',
    business_idea: businessIdeaId || 0,
    template: 0,
    description: ''
  });

  // Search and filter
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('');

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [templatesData, businessIdeasData] = await Promise.all([
        businessPlanTemplatesAPI.getTemplates(),
        businessIdeasAPI.getBusinessIdeas()
      ]);

      setTemplates(templatesData);
      setBusinessIdeas(businessIdeasData);

      // Set default business idea if provided
      if (businessIdeaId) {
        const selectedIdea = businessIdeasData.find(idea => idea.id === businessIdeaId);
        if (selectedIdea) {
          setFormData(prev => ({
            ...prev,
            title: `${selectedIdea.title} - Business Plan`,
            business_idea: businessIdeaId
          }));
        }
      }
    } catch (err) {
      setError(t('businessPlan.loadError'));
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesIndustry = !selectedIndustry || template.industry === selectedIndustry;
    return matchesSearch && matchesIndustry;
  });

  const industries = [...new Set(templates.map(t => t.industry))];

  const handleTemplateSelect = (template: BusinessPlanTemplate) => {
    setSelectedTemplate(template);
    setFormData(prev => ({
      ...prev,
      template: template.id
    }));
  };

  const handleNextStep = () => {
    if (step === 'template' && selectedTemplate) {
      setStep('details');
    }
  };

  const handlePreviousStep = () => {
    if (step === 'details') {
      setStep('template');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'business_idea' || name === 'template' ? parseInt(value) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTemplate || !formData.title.trim()) {
      setError(t('businessPlan.fillRequiredFields'));
      return;
    }

    try {
      setCreating(true);
      setStep('creating');
      setError(null);

      const businessPlan = await businessPlansAPI.createPlan({
        title: formData.title,
        business_idea: formData.business_idea || undefined,
        template: formData.template,
        status: 'draft'
      });

      if (onSuccess) {
        onSuccess(businessPlan);
      } else {
        navigate(`/dashboard/business-plans/${businessPlan.id}`);
      }
    } catch (err) {
      setError(t('businessPlan.createError'));
      setStep('details');
      console.error('Error creating business plan:', err);
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600/20 to-indigo-600/20 p-6 border-b border-white/10">
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <FileText size={24} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('businessPlan.createNew')}
              </h2>
              <p className="text-gray-300 text-sm">
                {step === 'template' && t('businessPlan.selectTemplate')}
                {step === 'details' && t('businessPlan.enterDetails')}
                {step === 'creating' && t('businessPlan.creating')}
              </p>
            </div>
          </div>
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          )}
        </div>

        {/* Progress Steps */}
        <div className={`flex items-center mt-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step === 'template' ? 'bg-purple-500 text-white' : 'bg-purple-500 text-white'
            }`}>
              {step !== 'template' ? <Check size={16} /> : '1'}
            </div>
            <span className={`text-sm text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {t('businessPlan.selectTemplate')}
            </span>
          </div>
          <div className={`w-8 h-0.5 bg-gray-600 ${isRTL ? 'mr-4 ml-4' : 'mx-4'}`}></div>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step === 'details' ? 'bg-purple-500 text-white' : 
              step === 'creating' ? 'bg-purple-500 text-white' : 'bg-gray-600 text-gray-400'
            }`}>
              {step === 'creating' ? <Check size={16} /> : '2'}
            </div>
            <span className={`text-sm ${
              step === 'template' ? 'text-gray-400' : 'text-white'
            } ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {t('businessPlan.enterDetails')}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {error && (
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertCircle size={20} className={`text-red-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <span className="text-red-300">{error}</span>
            </div>
          </div>
        )}

        {step === 'template' && (
          <div>
            {/* Search and Filter */}
            <div className={`flex flex-col md:flex-row gap-4 mb-6 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
              <div className="flex-1 relative">
                <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type="text"
                  placeholder={t('businessPlan.searchTemplates')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full bg-gray-800 border border-gray-600 rounded-lg py-2 text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'
                  }`}
                />
              </div>
              <div className="relative">
                <Filter size={20} className={`absolute top-3 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <select
                  value={selectedIndustry}
                  onChange={(e) => setSelectedIndustry(e.target.value)}
                  className={`bg-gray-800 border border-gray-600 rounded-lg py-2 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'
                  }`}
                >
                  <option value="">{t('businessPlan.allIndustries')}</option>
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Templates Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {filteredTemplates.map(template => (
                <div
                  key={template.id}
                  onClick={() => handleTemplateSelect(template)}
                  className={`bg-gray-800/50 border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-purple-500/50 ${
                    selectedTemplate?.id === template.id
                      ? 'border-purple-500 bg-purple-500/10'
                      : 'border-gray-600'
                  }`}
                >
                  <div className={`flex items-start justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-1">
                      <h3 className="text-white font-medium mb-1">{template.name}</h3>
                      <p className="text-gray-400 text-sm">{template.industry}</p>
                    </div>
                    {template.is_featured && (
                      <Star size={16} className="text-yellow-400" />
                    )}
                  </div>
                  
                  <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                    {template.description}
                  </p>

                  <div className={`flex items-center justify-between text-xs text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Clock size={12} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                      <span>{template.estimated_time}h</span>
                    </div>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Users size={12} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                      <span>{template.usage_count}</span>
                    </div>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <TrendingUp size={12} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                      <span>{template.rating}/5</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Next Button */}
            <div className={`flex justify-end ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={handleNextStep}
                disabled={!selectedTemplate}
                className={`bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors flex items-center ${
                  isRTL ? 'flex-row-reverse' : ''
                }`}
              >
                <span>{t('common.next')}</span>
                <ArrowRight size={16} className={`${isRTL ? 'mr-2' : 'ml-2'}`} />
              </button>
            </div>
          </div>
        )}

        {step === 'details' && selectedTemplate && (
          <form onSubmit={handleSubmit}>
            {/* Selected Template Info */}
            <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4 mb-6">
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <FileText size={20} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                <div>
                  <h3 className="text-white font-medium">{selectedTemplate.name}</h3>
                  <p className="text-gray-300 text-sm">{selectedTemplate.industry}</p>
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="space-y-6">
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('businessPlan.title')} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 ${
                    isRTL ? 'text-right' : ''
                  }`}
                  placeholder={t('businessPlan.titlePlaceholder')}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('businessPlan.businessIdea')}
                </label>
                <select
                  name="business_idea"
                  value={formData.business_idea}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white ${
                    isRTL ? 'text-right' : ''
                  }`}
                >
                  <option value={0}>{t('businessPlan.noBusinessIdea')}</option>
                  {businessIdeas.map(idea => (
                    <option key={idea.id} value={idea.id}>{idea.title}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('businessPlan.description')}
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${
                    isRTL ? 'text-right' : ''
                  }`}
                  placeholder={t('businessPlan.descriptionPlaceholder')}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className={`flex justify-between mt-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                type="button"
                onClick={handlePreviousStep}
                className={`bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors flex items-center ${
                  isRTL ? 'flex-row-reverse' : ''
                }`}
              >
                <ArrowLeft size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span>{t('common.previous')}</span>
              </button>

              <button
                type="submit"
                disabled={creating}
                className={`bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors flex items-center ${
                  isRTL ? 'flex-row-reverse' : ''
                }`}
              >
                {creating ? (
                  <>
                    <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                    <span>{t('businessPlan.creating')}</span>
                  </>
                ) : (
                  <>
                    <Sparkles size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span>{t('businessPlan.create')}</span>
                  </>
                )}
              </button>
            </div>
          </form>
        )}

        {step === 'creating' && (
          <div className="text-center py-12">
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('businessPlan.creatingPlan')}
            </h3>
            <p className="text-gray-400">
              {t('businessPlan.creatingPlanDescription')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessPlanCreateForm;
