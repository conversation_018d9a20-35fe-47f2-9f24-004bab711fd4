#!/usr/bin/env python
"""
Simple test script to verify Django API is working
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings_minimal')

# Setup Django
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from rest_framework.test import APIClient
import json

def test_basic_api():
    """Test basic API functionality"""
    print("🔍 Testing Django API...")
    
    # Create a test client
    client = APIClient()
    
    # Test basic health check
    try:
        response = client.get('/api/')
        print(f"✅ API root endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ API root endpoint failed: {e}")
    
    # Test business plan templates endpoint
    try:
        response = client.get('/api/incubator/business-plan-templates/')
        print(f"✅ Business plan templates endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 Found {len(data.get('results', []))} templates")
    except Exception as e:
        print(f"❌ Business plan templates endpoint failed: {e}")
    
    # Test user registration endpoint
    try:
        response = client.post('/api/auth/register/', {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User'
        })
        print(f"✅ User registration endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ User registration endpoint failed: {e}")

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing Database Connection...")
    
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"✅ Found {len(tables)} database tables")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")

def test_models():
    """Test model imports and basic functionality"""
    print("\n🔍 Testing Models...")
    
    try:
        from incubator.models_business_plan import BusinessPlanTemplate
        templates = BusinessPlanTemplate.objects.all()
        print(f"✅ BusinessPlanTemplate model: {templates.count()} templates")
    except Exception as e:
        print(f"❌ BusinessPlanTemplate model failed: {e}")
    
    try:
        from users.models import UserProfile
        profiles = UserProfile.objects.all()
        print(f"✅ UserProfile model: {profiles.count()} profiles")
    except Exception as e:
        print(f"❌ UserProfile model failed: {e}")

if __name__ == '__main__':
    print("🚀 Starting Django API Tests...\n")
    
    test_database_connection()
    test_models()
    test_basic_api()
    
    print("\n✅ API testing complete!")
