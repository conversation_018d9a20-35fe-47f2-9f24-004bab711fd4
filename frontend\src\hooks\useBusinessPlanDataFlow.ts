/**
 * Unified Business Plan Data Flow Hook
 * Coordinates between Redux state, React Query cache, and backend API
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { 
  fetchBusinessPlan,
  fetchBusinessPlanSections,
  updateBusinessPlanSection,
  syncBusinessPlanFromBackend,
  syncSectionFromBackend,
  selectCurrentBusinessPlan,
  selectBusinessPlanSections,
  selectIsLoading
} from '../store/businessPlansSlice';
import { businessPlansAPI, businessPlanSectionsAPI } from '../services/businessPlanApi';
import { businessPlanSyncService } from '../services/businessPlanSyncService';

/**
 * Query keys for React Query
 */
export const businessPlanQueryKeys = {
  all: ['businessPlans'] as const,
  lists: () => [...businessPlanQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...businessPlanQueryKeys.lists(), { filters }] as const,
  details: () => [...businessPlanQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...businessPlanQueryKeys.details(), id] as const,
  sections: (planId: number) => [...businessPlanQueryKeys.detail(planId), 'sections'] as const,
  section: (sectionId: number) => [...businessPlanQueryKeys.all, 'section', sectionId] as const,
};

/**
 * Hook for unified business plan data management
 */
export const useBusinessPlanDataFlow = (businessPlanId: number) => {
  const dispatch = useAppDispatch();
  const queryClient = useQueryClient();

  // Redux selectors
  const reduxBusinessPlan = useAppSelector(selectCurrentBusinessPlan);
  const reduxSections = useAppSelector(selectBusinessPlanSections);
  const reduxLoading = useAppSelector(selectIsLoading);

  // React Query for business plan
  const {
    data: queryBusinessPlan,
    isLoading: queryPlanLoading,
    error: queryPlanError,
    refetch: refetchPlan
  } = useQuery({
    queryKey: businessPlanQueryKeys.detail(businessPlanId),
    queryFn: () => businessPlansAPI.getPlan(businessPlanId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!businessPlanId,
  });

  // React Query for sections
  const {
    data: querySections,
    isLoading: querySectionsLoading,
    error: querySectionsError,
    refetch: refetchSections
  } = useQuery({
    queryKey: businessPlanQueryKeys.sections(businessPlanId),
    queryFn: () => businessPlanSectionsAPI.getSections(businessPlanId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!businessPlanId,
  });

  // Sync React Query data to Redux when it changes
  useEffect(() => {
    if (queryBusinessPlan && (!reduxBusinessPlan || reduxBusinessPlan.id !== queryBusinessPlan.id)) {
      dispatch(syncBusinessPlanFromBackend(queryBusinessPlan));
    }
  }, [queryBusinessPlan, reduxBusinessPlan, dispatch]);

  useEffect(() => {
    if (querySections && querySections.length > 0) {
      querySections.forEach(section => {
        const existingSection = reduxSections.find(s => s.id === section.id);
        if (!existingSection || existingSection.updated_at !== section.updated_at) {
          dispatch(syncSectionFromBackend(section));
        }
      });
    }
  }, [querySections, reduxSections, dispatch]);

  // Mutation for updating sections with optimistic updates
  const updateSectionMutation = useMutation({
    mutationFn: async ({ sectionId, data }: { sectionId: number; data: any }) => {
      return businessPlanSectionsAPI.updateSection(sectionId, data);
    },
    onMutate: async ({ sectionId, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: businessPlanQueryKeys.sections(businessPlanId) 
      });

      // Snapshot previous value
      const previousSections = queryClient.getQueryData(
        businessPlanQueryKeys.sections(businessPlanId)
      );

      // Optimistically update cache
      queryClient.setQueryData(
        businessPlanQueryKeys.sections(businessPlanId),
        (old: any[]) => {
          if (!old) return old;
          return old.map(section => 
            section.id === sectionId 
              ? { ...section, ...data, updated_at: new Date().toISOString() }
              : section
          );
        }
      );

      return { previousSections };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousSections) {
        queryClient.setQueryData(
          businessPlanQueryKeys.sections(businessPlanId),
          context.previousSections
        );
      }
    },
    onSuccess: (updatedSection) => {
      // Update Redux state
      dispatch(syncSectionFromBackend(updatedSection));
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: businessPlanQueryKeys.detail(businessPlanId)
      });
    },
  });

  // Unified data with preference for Redux state (more up-to-date for editing)
  const businessPlan = useMemo(() => {
    return reduxBusinessPlan || queryBusinessPlan;
  }, [reduxBusinessPlan, queryBusinessPlan]);

  const sections = useMemo(() => {
    return reduxSections.length > 0 ? reduxSections : (querySections || []);
  }, [reduxSections, querySections]);

  // Unified loading state
  const isLoading = reduxLoading || queryPlanLoading || querySectionsLoading;

  // Unified error state
  const error = queryPlanError || querySectionsError;

  // Methods for data manipulation
  const updateSection = useCallback(async (sectionId: number, data: any) => {
    return updateSectionMutation.mutateAsync({ sectionId, data });
  }, [updateSectionMutation]);

  const refreshData = useCallback(async () => {
    await Promise.all([
      refetchPlan(),
      refetchSections()
    ]);
  }, [refetchPlan, refetchSections]);

  const syncWithBackend = useCallback(async () => {
    await businessPlanSyncService.syncBusinessPlan(businessPlanId, { forceRefresh: true });
    await businessPlanSyncService.syncBusinessPlanSections(businessPlanId, { forceRefresh: true });
  }, [businessPlanId]);

  const invalidateCache = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: businessPlanQueryKeys.detail(businessPlanId)
    });
    queryClient.invalidateQueries({
      queryKey: businessPlanQueryKeys.sections(businessPlanId)
    });
  }, [queryClient, businessPlanId]);

  return {
    // Data
    businessPlan,
    sections,
    
    // Loading states
    isLoading,
    isPlanLoading: queryPlanLoading,
    isSectionsLoading: querySectionsLoading,
    isUpdating: updateSectionMutation.isPending,
    
    // Error states
    error,
    planError: queryPlanError,
    sectionsError: querySectionsError,
    updateError: updateSectionMutation.error,
    
    // Methods
    updateSection,
    refreshData,
    syncWithBackend,
    invalidateCache,
    
    // Query utilities
    refetchPlan,
    refetchSections,
  };
};

/**
 * Hook for business plan list with unified caching
 */
export const useBusinessPlansList = (filters: Record<string, any> = {}) => {
  const queryClient = useQueryClient();

  const {
    data: businessPlans = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: businessPlanQueryKeys.list(filters),
    queryFn: () => businessPlansAPI.getPlans(filters.businessIdeaId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  const invalidateList = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: businessPlanQueryKeys.lists()
    });
  }, [queryClient]);

  return {
    businessPlans,
    isLoading,
    error,
    refetch,
    invalidateList,
  };
};

/**
 * Hook for section-specific operations
 */
export const useBusinessPlanSection = (sectionId: number) => {
  const queryClient = useQueryClient();

  const {
    data: section,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: businessPlanQueryKeys.section(sectionId),
    queryFn: () => businessPlanSectionsAPI.getSection(sectionId),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    enabled: !!sectionId,
  });

  const updateSectionMutation = useMutation({
    mutationFn: (data: any) => businessPlanSectionsAPI.updateSection(sectionId, data),
    onSuccess: (updatedSection) => {
      // Update cache
      queryClient.setQueryData(
        businessPlanQueryKeys.section(sectionId),
        updatedSection
      );
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: businessPlanQueryKeys.sections(updatedSection.business_plan)
      });
    },
  });

  const updateSection = useCallback((data: any) => {
    return updateSectionMutation.mutateAsync(data);
  }, [updateSectionMutation]);

  return {
    section,
    isLoading,
    error,
    isUpdating: updateSectionMutation.isPending,
    updateError: updateSectionMutation.error,
    updateSection,
    refetch,
  };
};
