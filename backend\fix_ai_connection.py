#!/usr/bin/env python
"""
AI Connection Fix Script
This script will diagnose and fix AI connection issues
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def main():
    print("🔧 AI Connection Fix Script")
    print("=" * 50)
    
    try:
        # Import required modules
        from core.ai_config import get_gemini_config, reload_gemini_config
        from core.models import AIConfiguration
        from django.contrib.auth.models import User
        
        print("📋 Step 1: Checking current AI status...")
        config = get_gemini_config()
        status = config.get_status()
        
        print(f"   Service: {status['service']}")
        print(f"   Available: {status['available']}")
        print(f"   Model: {status['model']}")
        print(f"   API Key Configured: {status['api_key_configured']}")
        
        if status['available']:
            print("✅ AI service is already working!")
            return
            
        print("\n🔄 Step 2: Reloading AI configuration...")
        reload_gemini_config()
        
        # Check status again
        config = get_gemini_config()
        status = config.get_status()
        
        if status['available']:
            print("✅ AI service fixed after reload!")
            return
            
        print("\n🔧 Step 3: Reinitializing AI configuration...")
        
        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        # Initialize AI configuration from environment
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBLcSmyOVNJyKq_X6T3MOjio6XmZLliX5s')
        
        configs = [
            {
                'key': 'api_key',
                'value': api_key,
                'config_type': 'api_key',
                'is_sensitive': True,
                'description': 'Google Gemini API Key for AI services'
            },
            {
                'key': 'default_model',
                'value': 'gemini-1.5-flash',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Default Gemini model to use'
            },
            {
                'key': 'max_tokens',
                'value': '4000',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Maximum tokens per request'
            },
            {
                'key': 'temperature',
                'value': '0.7',
                'config_type': 'model_config',
                'is_sensitive': False,
                'description': 'Model temperature for response creativity'
            }
        ]
        
        # Create or update configurations
        for config_data in configs:
            try:
                config_obj, created = AIConfiguration.objects.update_or_create(
                    provider='gemini',
                    key=config_data['key'],
                    defaults={
                        'value': config_data['value'],
                        'config_type': config_data['config_type'],
                        'is_sensitive': config_data['is_sensitive'],
                        'description': config_data['description'],
                        'is_active': True,
                        'updated_by': admin_user,
                        'created_by': admin_user if created else None
                    }
                )
                
                action = 'Created' if created else 'Updated'
                print(f'   ✅ {action} config: {config_data["key"]}')
                
            except Exception as e:
                print(f'   ❌ Failed to create config {config_data["key"]}: {e}')
        
        print("\n🔄 Step 4: Reloading configuration after update...")
        reload_gemini_config()
        
        # Final status check
        config = get_gemini_config()
        status = config.get_status()
        
        print(f"\n📊 Final Status:")
        print(f"   Service: {status['service']}")
        print(f"   Available: {status['available']}")
        print(f"   Model: {status['model']}")
        print(f"   API Key Configured: {status['api_key_configured']}")
        
        if status['available']:
            print("\n🧪 Step 5: Testing AI service...")
            try:
                test_response = config.generate_content("Hello! Please respond with 'AI is working correctly'")
                if test_response:
                    print(f"✅ AI Test Response: {test_response}")
                    print("\n🎉 SUCCESS: AI service is now working!")
                else:
                    print("❌ AI test failed - no response")
            except Exception as e:
                print(f"❌ AI test failed: {e}")
        else:
            print("\n❌ AI service is still not available")
            print("   Check the logs for more details about the initialization failure")
            
    except Exception as e:
        print(f"❌ Error during AI fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
