import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  AlertTriangle,
  Trash2,
  X,
  FileText,
  Calendar,
  User,
  CheckCircle
} from 'lucide-react';
import {
  BusinessPlan,
  businessPlansAPI
} from '../../services/businessPlanApi';

interface BusinessPlanDeleteModalProps {
  businessPlan: BusinessPlan;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const BusinessPlanDeleteModal: React.FC<BusinessPlanDeleteModalProps> = ({
  businessPlan,
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');

  const requiredConfirmText = 'DELETE';

  const handleDelete = async () => {
    if (confirmText !== requiredConfirmText) {
      setError(t('businessPlan.confirmTextMismatch'));
      return;
    }

    try {
      setDeleting(true);
      setError(null);

      await businessPlansAPI.deletePlan(businessPlan.id);
      
      onSuccess();
      onClose();
    } catch (err) {
      setError(t('businessPlan.deleteError'));
      console.error('Error deleting business plan:', err);
    } finally {
      setDeleting(false);
    }
  };

  const handleClose = () => {
    if (!deleting) {
      setConfirmText('');
      setError(null);
      onClose();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar' : 'en', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-red-500/30 rounded-lg max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="bg-red-600/20 border-b border-red-500/30 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertTriangle size={24} className={`text-red-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
              <div>
                <h2 className="text-xl font-bold text-white">
                  {t('businessPlan.deleteTitle')}
                </h2>
                <p className="text-red-300 text-sm">
                  {t('businessPlan.deleteWarning')}
                </p>
              </div>
            </div>
            
            <button
              onClick={handleClose}
              disabled={deleting}
              className="text-gray-400 hover:text-white transition-colors disabled:cursor-not-allowed"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Business Plan Info */}
          <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4 mb-6">
            <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FileText size={20} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`} />
              <div className="flex-1">
                <h3 className="text-white font-semibold mb-2">{businessPlan.title}</h3>
                
                <div className="space-y-2 text-sm text-gray-400">
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Calendar size={14} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span>{t('common.created')}: {formatDate(businessPlan.created_at)}</span>
                  </div>
                  
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <User size={14} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span>{t('businessPlan.status')}: {t(`businessPlan.status.${businessPlan.status}`)}</span>
                  </div>

                  {businessPlan.completion_percentage !== undefined && (
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <CheckCircle size={14} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <span>{businessPlan.completion_percentage}% {t('common.complete')}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-6">
            <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertTriangle size={20} className={`text-red-400 ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`} />
              <div className="flex-1">
                <h4 className="text-red-300 font-semibold mb-2">
                  {t('businessPlan.deleteConsequences')}
                </h4>
                <ul className={`text-red-200 text-sm space-y-1 ${isRTL ? 'text-right' : ''}`}>
                  <li>• {t('businessPlan.deleteConsequence1')}</li>
                  <li>• {t('businessPlan.deleteConsequence2')}</li>
                  <li>• {t('businessPlan.deleteConsequence3')}</li>
                  <li>• {t('businessPlan.deleteConsequence4')}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="mb-6">
            <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
              {t('businessPlan.confirmDeleteText', { text: requiredConfirmText })}
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={requiredConfirmText}
              className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-white placeholder-gray-400 ${
                isRTL ? 'text-right' : ''
              }`}
              disabled={deleting}
            />
            {confirmText && confirmText !== requiredConfirmText && (
              <p className="text-red-400 text-sm mt-1">
                {t('businessPlan.confirmTextMismatch')}
              </p>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AlertTriangle size={20} className={`text-red-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span className="text-red-300">{error}</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={handleClose}
              disabled={deleting}
              className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors"
            >
              {t('common.cancel')}
            </button>
            
            <button
              onClick={handleDelete}
              disabled={deleting || confirmText !== requiredConfirmText}
              className={`bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors flex items-center ${
                isRTL ? 'flex-row-reverse' : ''
              }`}
            >
              {deleting ? (
                <>
                  <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                  <span>{t('businessPlan.deleting')}</span>
                </>
              ) : (
                <>
                  <Trash2 size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <span>{t('businessPlan.deleteConfirm')}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanDeleteModal;
