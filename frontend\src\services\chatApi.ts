/**
 * Chat API Service
 * Handles all chat-related API calls
 */

import { apiRequest } from './api';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  action_result?: {
    success: boolean;
    message: string;
  };
}

interface ChatAnalytics {
  total_messages: number;
  total_sessions: number;
  average_response_time: number;
  recent_activity: {
    messages_last_30_days: number;
    sessions_last_30_days: number;
  };
  language_distribution: { arabic: number; english: number };
}

// Chat storage utilities
const CHAT_STORAGE_KEY = 'yasmeen_chat_history';
const MAX_STORED_SESSIONS = 50;

interface StoredChatSession {
  id: string;
  businessIdeaId?: number;
  messages: ChatMessage[];
  lastUpdated: string;
}

const getChatStorageKey = (businessIdeaId?: number) => {
  return businessIdeaId ? `${CHAT_STORAGE_KEY}_${businessIdeaId}` : CHAT_STORAGE_KEY;
};

export const chatAPI = {
  async getChatHistory(businessIdeaId?: number): Promise<ChatMessage[]> {
    try {
      const storageKey = getChatStorageKey(businessIdeaId);
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const session: StoredChatSession = JSON.parse(stored);
        return session.messages || [];
      }
      return [];
    } catch (error) {
      console.error('Error loading chat history:', error);
      return [];
    }
  },

  async storeMessage(message: ChatMessage, businessIdeaId?: number): Promise<void> {
    try {
      const storageKey = getChatStorageKey(businessIdeaId);
      const existing = await this.getChatHistory(businessIdeaId);
      const updated = [...existing, message];

      const session: StoredChatSession = {
        id: businessIdeaId?.toString() || 'default',
        businessIdeaId,
        messages: updated.slice(-100), // Keep last 100 messages
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(storageKey, JSON.stringify(session));
    } catch (error) {
      console.error('Error storing message:', error);
    }
  },

  async clearChatHistory(businessIdeaId?: number): Promise<void> {
    try {
      const storageKey = getChatStorageKey(businessIdeaId);
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Error clearing chat history:', error);
    }
  },

  async sendMessage(message: string, businessIdeaId?: number): Promise<ChatMessage> {
    try {
      // Use the centralized API request function with proper authentication
      const response = await apiRequest<{
        data?: { message: string };
        message?: string;
        action_result?: any;
      }>('/ai/chat/', 'POST', {
        message,
        language: 'auto',
        business_idea_id: businessIdeaId
      });

      return {
        role: 'assistant',
        content: response.data?.message || response.message || 'Sorry, I could not process your request.',
        timestamp: new Date().toISOString(),
        action_result: response.action_result
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }
};

export const enhancedChatAPI = {
  async getChatAnalytics(): Promise<ChatAnalytics> {
    // Mock data for now - in real implementation, this would call the backend
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          total_messages: 1247,
          total_sessions: 89,
          average_response_time: 1.2,
          recent_activity: {
            messages_last_30_days: 342,
            sessions_last_30_days: 28
          },
          language_distribution: { arabic: 65, english: 35 }
        });
      }, 1000);
    });
  }
};
