/**
 * End-to-End User Journey Tests
 * Tests complete user workflows to ensure all functionality works together correctly
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

// Import test utilities and mocks
import { businessPlansAPI } from '../services/businessPlanApi';
import { businessPlanAnalyticsAPI } from '../services/businessPlanAnalyticsApi';
import { centralizedAiApi } from '../services/centralizedAiApi';

// Import main app components
import App from '../App';
import BusinessPlanPage from '../pages/dashboard/BusinessPlanPage';
import BusinessIdeaGenerator from '../components/ai/BusinessIdeaGenerator';

// Test wrapper setup
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const store = configureStore({
    reducer: {
      auth: (state = { 
        user: { id: 1, role: 'entrepreneur', name: 'Test User' },
        isAuthenticated: true 
      }) => state,
      businessPlans: (state = {
        businessPlans: [],
        currentBusinessPlan: null,
        sections: [],
        isLoading: false,
        error: null
      }) => state
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </QueryClientProvider>
    </Provider>
  );
};

describe('End-to-End User Journey Tests', () => {
  let testWrapper: any;
  let user: any;

  beforeEach(() => {
    testWrapper = createTestWrapper();
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  describe('Entrepreneur Complete Journey', () => {
    it('should complete full business plan creation workflow', async () => {
      // Mock API responses for the journey
      const mockBusinessIdea = {
        id: 1,
        title: 'AI-Powered E-commerce Platform',
        description: 'An innovative e-commerce solution',
        industry: 'Technology'
      };

      const mockBusinessPlan = {
        id: 1,
        title: 'AI E-commerce Business Plan',
        business_idea: 1,
        sections: [
          {
            id: 1,
            section_type: 'executive_summary',
            title: 'Executive Summary',
            content: '',
            order: 1
          }
        ]
      };

      const mockAIContent = {
        success: true,
        data: {
          content: 'AI-generated executive summary content for the e-commerce platform...'
        }
      };

      // Setup API mocks
      vi.spyOn(businessPlansAPI, 'createPlan').mockResolvedValue(mockBusinessPlan);
      vi.spyOn(businessPlansAPI, 'getPlans').mockResolvedValue([mockBusinessPlan]);
      vi.spyOn(centralizedAiApi, 'generateIntelligentContent').mockResolvedValue(mockAIContent);

      // Step 1: Start with business idea generation
      render(<BusinessIdeaGenerator />, { wrapper: testWrapper });

      // Fill in business idea criteria
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.selectOptions(industrySelect, 'Technology');

      const interestsInput = screen.getByLabelText(/interests/i);
      await user.type(interestsInput, 'AI, E-commerce, Technology');

      // Generate business ideas
      const generateButton = screen.getByText(/generate.*ideas/i);
      await user.click(generateButton);

      // Wait for AI generation
      await waitFor(() => {
        expect(centralizedAiApi.generateIntelligentContent).toHaveBeenCalledWith({
          content_type: 'business_idea_generation',
          context: expect.objectContaining({
            industry: 'Technology',
            interests: ['AI', 'E-commerce', 'Technology']
          }),
          language: 'en'
        });
      });

      // Step 2: Create business plan from idea
      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Create new business plan
      const createPlanButton = screen.getByText(/create.*plan/i);
      await user.click(createPlanButton);

      // Wait for plan creation
      await waitFor(() => {
        expect(businessPlansAPI.createPlan).toHaveBeenCalled();
      });

      // Step 3: Use AI to generate content
      const aiGenerateButton = screen.getByText(/generate.*ai/i);
      await user.click(aiGenerateButton);

      // Wait for AI content generation
      await waitFor(() => {
        expect(centralizedAiApi.generateIntelligentContent).toHaveBeenCalledWith({
          content_type: 'business_plan_section',
          context: expect.any(Object),
          language: 'en'
        });
      });

      // Verify AI content is displayed
      expect(screen.getByText(/AI-generated executive summary/)).toBeInTheDocument();
    });

    it('should handle business plan export workflow', async () => {
      const mockPlan = {
        id: 1,
        title: 'Test Business Plan',
        sections: [
          { id: 1, title: 'Executive Summary', content: 'Real content here' }
        ]
      };

      const mockPDFBlob = new Blob(['PDF content'], { type: 'application/pdf' });

      vi.spyOn(businessPlansAPI, 'getPlan').mockResolvedValue(mockPlan);
      vi.spyOn(businessPlansAPI, 'exportPlan').mockResolvedValue(mockPDFBlob);

      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Wait for plan to load
      await waitFor(() => {
        expect(screen.getByText('Test Business Plan')).toBeInTheDocument();
      });

      // Export as PDF
      const exportButton = screen.getByText(/export.*pdf/i);
      await user.click(exportButton);

      // Verify export API call
      await waitFor(() => {
        expect(businessPlansAPI.exportPlan).toHaveBeenCalledWith(1, 'pdf');
      });
    });
  });

  describe('Investor Dashboard Journey', () => {
    it('should display real investment data and analytics', async () => {
      const mockInvestorData = {
        portfolioValue: 2500000,
        totalInvestments: 15,
        roi: 18.5,
        activeDeals: 8
      };

      const mockPortfolio = [
        {
          id: 1,
          companyName: 'TechStart AI',
          industry: 'Technology',
          investmentAmount: 250000,
          currentValue: 420000,
          performance: 68
        }
      ];

      // Mock investor dashboard APIs
      vi.spyOn(businessPlanAnalyticsAPI, 'getAnalytics').mockResolvedValue(mockInvestorData);

      // Render investor dashboard (would need to import actual component)
      // This is a simplified test - in reality would test the actual investor dashboard
      
      // Verify real data is loaded and displayed
      // expect(screen.getByText('$2,500,000')).toBeInTheDocument();
      // expect(screen.getByText('18.5%')).toBeInTheDocument();
    });
  });

  describe('Translation System Journey', () => {
    it('should switch languages and display translated content', async () => {
      render(<BusinessIdeaGenerator />, { wrapper: testWrapper });

      // Check initial English content
      expect(screen.getByText(/generate/i)).toBeInTheDocument();

      // Switch to Arabic (would need language switcher component)
      // const languageSwitch = screen.getByLabelText(/language/i);
      // await user.click(languageSwitch);
      // await user.click(screen.getByText('العربية'));

      // Verify Arabic content is displayed
      // expect(screen.getByText(/توليد/)).toBeInTheDocument();
    });
  });

  describe('Error Handling Journey', () => {
    it('should handle API failures gracefully', async () => {
      // Mock API failure
      vi.spyOn(businessPlansAPI, 'getPlans').mockRejectedValue(new Error('Network Error'));

      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });

      // Verify error message is user-friendly
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });

    it('should handle AI service failures with fallbacks', async () => {
      // Mock AI service failure
      vi.spyOn(centralizedAiApi, 'generateIntelligentContent').mockRejectedValue(
        new Error('AI Service Unavailable')
      );

      render(<BusinessIdeaGenerator />, { wrapper: testWrapper });

      // Try to generate ideas
      const generateButton = screen.getByText(/generate/i);
      await user.click(generateButton);

      // Wait for fallback content
      await waitFor(() => {
        expect(screen.getByText(/fallback/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Loading States', () => {
    it('should show loading states during data fetching', async () => {
      // Mock slow API response
      vi.spyOn(businessPlansAPI, 'getPlans').mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve([]), 2000))
      );

      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Verify loading state is shown
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should handle concurrent operations correctly', async () => {
      const mockPlan = { id: 1, title: 'Test Plan' };
      
      vi.spyOn(businessPlansAPI, 'updatePlan').mockResolvedValue(mockPlan);

      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Simulate concurrent save operations
      const saveButton = screen.getByText(/save/i);
      
      // Click save multiple times quickly
      await user.click(saveButton);
      await user.click(saveButton);
      await user.click(saveButton);

      // Verify only one save operation is processed
      await waitFor(() => {
        expect(businessPlansAPI.updatePlan).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Data Synchronization Journey', () => {
    it('should synchronize data across multiple components', async () => {
      const mockPlan = {
        id: 1,
        title: 'Synchronized Plan',
        updated_at: '2024-01-16T10:00:00Z'
      };

      vi.spyOn(businessPlansAPI, 'getPlan').mockResolvedValue(mockPlan);
      vi.spyOn(businessPlansAPI, 'updatePlan').mockResolvedValue({
        ...mockPlan,
        title: 'Updated Plan',
        updated_at: '2024-01-16T10:05:00Z'
      });

      render(<BusinessPlanPage />, { wrapper: testWrapper });

      // Wait for initial data load
      await waitFor(() => {
        expect(screen.getByText('Synchronized Plan')).toBeInTheDocument();
      });

      // Update the plan
      const editButton = screen.getByText(/edit/i);
      await user.click(editButton);

      const titleInput = screen.getByDisplayValue('Synchronized Plan');
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Plan');

      const saveButton = screen.getByText(/save/i);
      await user.click(saveButton);

      // Verify update is reflected
      await waitFor(() => {
        expect(screen.getByText('Updated Plan')).toBeInTheDocument();
      });
    });
  });
});
