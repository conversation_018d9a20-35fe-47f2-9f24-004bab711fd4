import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import DOMPurify from 'dompurify';

interface ModernRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  'aria-label'?: string;
}

/**
 * Modern Rich Text Editor Component
 * Replaces ReactQuill to fix deprecation warnings and improve security
 */
const ModernRichTextEditor: React.FC<ModernRichTextEditorProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  'aria-label': ariaLabel
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Sanitize HTML content to prevent XSS attacks
  const sanitizeContent = useCallback((content: string): string => {
    return DOMPurify.sanitize(content, {
      ALLOWED_TAGS: [
        'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 
        'ul', 'ol', 'li', 'blockquote', 'a'
      ],
      ALLOWED_ATTR: ['href', 'target', 'rel'],
      ALLOW_DATA_ATTR: false
    });
  }, []);

  // Handle content changes with sanitization
  const handleContentChange = useCallback((event: React.FormEvent<HTMLDivElement>) => {
    const content = event.currentTarget.innerHTML;
    const sanitizedContent = sanitizeContent(content);
    onChange(sanitizedContent);
  }, [onChange, sanitizeContent]);

  // Toolbar actions
  const execCommand = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value);
  }, []);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'b':
          event.preventDefault();
          execCommand('bold');
          break;
        case 'i':
          event.preventDefault();
          execCommand('italic');
          break;
        case 'u':
          event.preventDefault();
          execCommand('underline');
          break;
        case 's':
          event.preventDefault();
          // Trigger save action (handled by parent component)
          break;
      }
    }
  }, [execCommand]);

  // Memoized toolbar configuration
  const toolbarConfig = useMemo(() => [
    {
      group: 'format',
      buttons: [
        { command: 'bold', icon: '𝐁', title: t('editor.bold'), shortcut: 'Ctrl+B' },
        { command: 'italic', icon: '𝐼', title: t('editor.italic'), shortcut: 'Ctrl+I' },
        { command: 'underline', icon: '𝐔', title: t('editor.underline'), shortcut: 'Ctrl+U' }
      ]
    },
    {
      group: 'structure',
      buttons: [
        { command: 'formatBlock', value: 'h1', icon: 'H1', title: t('editor.heading1') },
        { command: 'formatBlock', value: 'h2', icon: 'H2', title: t('editor.heading2') },
        { command: 'formatBlock', value: 'h3', icon: 'H3', title: t('editor.heading3') }
      ]
    },
    {
      group: 'lists',
      buttons: [
        { command: 'insertUnorderedList', icon: '•', title: t('editor.bulletList') },
        { command: 'insertOrderedList', icon: '1.', title: t('editor.numberedList') }
      ]
    },
    {
      group: 'alignment',
      buttons: [
        { command: 'justifyLeft', icon: '⫷', title: t('editor.alignLeft') },
        { command: 'justifyCenter', icon: '⫸', title: t('editor.alignCenter') },
        { command: 'justifyRight', icon: '⫹', title: t('editor.alignRight') }
      ]
    }
  ], [t]);

  return (
    <div className={`modern-rich-text-editor ${className} ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Toolbar */}
      <div 
        className="editor-toolbar glass-light border-b border-white/20 p-2 flex flex-wrap gap-1"
        role="toolbar"
        aria-label={t('editor.toolbar')}
      >
        {toolbarConfig.map((group) => (
          <div key={group.group} className="toolbar-group flex gap-1 mr-2">
            {group.buttons.map((button) => (
              <button
                key={button.command + (button.value || '')}
                type="button"
                className="toolbar-button px-2 py-1 rounded text-white/80 hover:text-white hover:bg-white/10 transition-colors"
                onClick={() => execCommand(button.command, button.value)}
                title={`${button.title}${button.shortcut ? ` (${button.shortcut})` : ''}`}
                aria-label={button.title}
                disabled={disabled}
              >
                {button.icon}
              </button>
            ))}
          </div>
        ))}
      </div>

      {/* Editor Content */}
      <div
        className="editor-content glass-light p-4 min-h-[350px] text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
        contentEditable={!disabled}
        suppressContentEditableWarning={true}
        onInput={handleContentChange}
        onKeyDown={handleKeyDown}
        dangerouslySetInnerHTML={{ __html: sanitizeContent(value) }}
        dir={isRTL ? 'rtl' : 'ltr'}
        role="textbox"
        aria-multiline="true"
        aria-label={ariaLabel || t('editor.content')}
        data-placeholder={placeholder || t('businessPlan.startWriting')}
        style={{
          direction: isRTL ? 'rtl' : 'ltr',
          textAlign: isRTL ? 'right' : 'left'
        }}
      />

      {/* Character count and status */}
      <div className="editor-status glass-light border-t border-white/20 p-2 text-xs text-white/60 flex justify-between">
        <span>{t('editor.characters')}: {value.replace(/<[^>]*>/g, '').length}</span>
        <span>{disabled ? t('editor.readonly') : t('editor.editing')}</span>
      </div>
    </div>
  );
};

export default ModernRichTextEditor;