import { useAppSelector, useAppDispatch } from '../store/hooks';
import { login, logout, register, getCurrentUser, clearError, clearAuth } from '../store/authSlice';
import { User, getAuthToken, getRefreshToken, clearAuthTokens } from '../services/api';
import { createRoleManager } from '../utils/unifiedRoleManager';
import { useEffect, useCallback } from 'react';

/**
 * Enhanced custom hook for authentication state and actions
 * Provides a convenient interface to the auth Redux state with automatic token validation
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading, error } = useAppSelector(state => state.auth);

  /**
   * Check if authentication state is consistent with stored tokens
   */
  const validateAuthState = useCallback(() => {
    const token = getAuthToken();
    const refreshToken = getRefreshToken();

    // If Redux says we're authenticated but no tokens exist, clear auth state
    if (isAuthenticated && !token && !refreshToken) {
      console.log('🔐 Auth state inconsistent, clearing...');
      dispatch(clearAuth());
      return false;
    }

    // If we have tokens but Redux says we're not authenticated, try to get user
    if (token && !isAuthenticated && !isLoading) {
      console.log('🔐 Tokens exist but not authenticated in Redux, fetching user...');
      dispatch(getCurrentUser());
      return true;
    }

    return isAuthenticated;
  }, [isAuthenticated, isLoading, dispatch]);

  /**
   * Validate auth state on mount and when auth state changes
   */
  useEffect(() => {
    validateAuthState();
  }, [validateAuthState]);

  /**
   * Login user with username and password
   */
  const loginUser = async (username: string, password: string) => {
    const result = await dispatch(login({ username, password }));
    return result;
  };

  /**
   * Logout current user
   */
  const logoutUser = async () => {
    const result = await dispatch(logout());
    return result;
  };

  /**
   * Register new user
   */
  const registerUser = async (userData: {
    username: string;
    email: string;
    password: string;
    password_confirm: string;
    first_name?: string;
    last_name?: string;
  }) => {
    const result = await dispatch(register(userData));
    return result;
  };

  /**
   * Fetch current user data
   */
  const fetchCurrentUser = async () => {
    const result = await dispatch(getCurrentUser());
    return result;
  };

  /**
   * Clear authentication error
   */
  const clearAuthError = () => {
    dispatch(clearError());
  };

  /**
   * Check if user has specific role
   * @deprecated Use createRoleManager(user).hasRole() instead
   */
  const hasRole = (role: string): boolean => {
    // Import here to avoid circular dependencies
    return createRoleManager(user).hasRole(role as any);
  };

  /**
   * Check if user is admin
   */
  const isAdmin = (): boolean => {
    return hasRole('admin');
  };

  /**
   * Check if user is moderator
   */
  const isModerator = (): boolean => {
    return hasRole('moderator') || isAdmin();
  };

  /**
   * Check if user is mentor
   */
  const isMentor = (): boolean => {
    return hasRole('mentor');
  };

  /**
   * Check if user is investor
   */
  const isInvestor = (): boolean => {
    return hasRole('investor');
  };

  /**
   * Get user's primary role
   */
  const getPrimaryRole = (): string => {
    if (!user || !user.profile) return 'user';

    // Use the primary_role if available
    if (user.profile.primary_role) {
      return user.profile.primary_role.name;
    }

    // Fallback to first active role
    if (user.profile.active_roles && user.profile.active_roles.length > 0) {
      return user.profile.active_roles[0].name;
    }

    return 'user';
  };

  /**
   * Get all user roles
   */
  const getUserRoles = (): string[] => {
    if (!user || !user.profile || !user.profile.active_roles) return ['user'];
    const roleNames = user.profile.active_roles.map(role => role.name);
    return roleNames.length > 0 ? roleNames : ['user'];
  };

  /**
   * Check if user has any of the specified roles
   */
  const hasAnyRole = (roles: string[]): boolean => {
    if (!user || !user.profile || !user.profile.active_roles) return false;
    return roles.some(role => user.profile.active_roles.some(userRole => userRole.name === role));
  };

  /**
   * Check if user has all of the specified roles
   */
  const hasAllRoles = (roles: string[]): boolean => {
    if (!user || !user.profile || !user.profile.active_roles) return false;
    return roles.every(role => user.profile.active_roles.some(userRole => userRole.name === role));
  };

  /**
   * Get user's display name
   */
  const getDisplayName = (): string => {
    if (!user) return '';
    
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    
    if (user.first_name) {
      return user.first_name;
    }
    
    return user.username || user.email || '';
  };

  /**
   * Get user's initials for avatar
   */
  const getInitials = (): string => {
    if (!user) return '';
    
    if (user.first_name && user.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
    }
    
    if (user.first_name) {
      return user.first_name[0].toUpperCase();
    }
    
    if (user.username) {
      return user.username[0].toUpperCase();
    }
    
    if (user.email) {
      return user.email[0].toUpperCase();
    }
    
    return '';
  };

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login: loginUser,
    logout: logoutUser,
    register: registerUser,
    getCurrentUser: fetchCurrentUser,
    clearError: clearAuthError,
    
    // Role checks
    hasRole,
    isAdmin,
    isModerator,
    isMentor,
    isInvestor,
    getPrimaryRole,
    getUserRoles,
    hasAnyRole,
    hasAllRoles,
    
    // User info
    getDisplayName,
    getInitials,
  };
};

export default useAuth;
