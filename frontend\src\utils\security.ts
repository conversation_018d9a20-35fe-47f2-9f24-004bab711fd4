import DOMPurify from 'dompurify';

/**
 * Security utilities for input sanitization and validation
 */

// Configure DOMPurify with strict settings
const configureDOMPurify = () => {
  // Add custom hooks for additional security
  DOMPurify.addHook('beforeSanitizeElements', (node) => {
    // Remove any script tags completely
    if (node.tagName === 'SCRIPT') {
      node.remove();
    }
  });

  DOMPurify.addHook('beforeSanitizeAttributes', (node) => {
    // Remove any javascript: URLs
    if (node.hasAttribute('href')) {
      const href = node.getAttribute('href');
      if (href && href.toLowerCase().startsWith('javascript:')) {
        node.removeAttribute('href');
      }
    }
  });
};

// Initialize DOMPurify configuration
configureDOMPurify();

/**
 * Sanitize HTML content for rich text editors
 * @param content - Raw HTML content
 * @returns Sanitized HTML content
 */
export const sanitizeRichTextContent = (content: string): string => {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'a', 'span', 'div'
    ],
    ALLOWED_ATTR: [
      'href', 'target', 'rel', 'class', 'style'
    ],
    ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_TRUSTED_TYPE: false
  });
};

/**
 * Sanitize plain text input
 * @param text - Raw text input
 * @returns Sanitized text
 */
export const sanitizeTextInput = (text: string): string => {
  return DOMPurify.sanitize(text, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });
};

/**
 * Validate business plan title
 * @param title - Business plan title
 * @returns Validation result
 */
export const validateBusinessPlanTitle = (title: string): {
  isValid: boolean;
  error?: string;
  sanitized: string;
} => {
  const sanitized = sanitizeTextInput(title.trim());
  
  if (!sanitized) {
    return {
      isValid: false,
      error: 'Title is required',
      sanitized: ''
    };
  }
  
  if (sanitized.length < 3) {
    return {
      isValid: false,
      error: 'Title must be at least 3 characters long',
      sanitized
    };
  }
  
  if (sanitized.length > 100) {
    return {
      isValid: false,
      error: 'Title must be less than 100 characters',
      sanitized: sanitized.substring(0, 100)
    };
  }
  
  // Check for potentially malicious patterns
  const maliciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /data:text\/html/i
  ];
  
  for (const pattern of maliciousPatterns) {
    if (pattern.test(title)) {
      return {
        isValid: false,
        error: 'Title contains invalid characters',
        sanitized
      };
    }
  }
  
  return {
    isValid: true,
    sanitized
  };
};

/**
 * Validate business plan content
 * @param content - Business plan section content
 * @returns Validation result
 */
export const validateBusinessPlanContent = (content: string): {
  isValid: boolean;
  error?: string;
  sanitized: string;
} => {
  const sanitized = sanitizeRichTextContent(content);
  
  // Check content length (max 50,000 characters)
  if (sanitized.length > 50000) {
    return {
      isValid: false,
      error: 'Content is too long (maximum 50,000 characters)',
      sanitized: sanitized.substring(0, 50000)
    };
  }
  
  // Check for excessive nesting (potential DoS)
  const nestingLevel = (sanitized.match(/<[^>]+>/g) || []).length;
  if (nestingLevel > 1000) {
    return {
      isValid: false,
      error: 'Content structure is too complex',
      sanitized: sanitizeTextInput(content) // Fallback to plain text
    };
  }
  
  return {
    isValid: true,
    sanitized
  };
};

/**
 * Validate file upload
 * @param file - File to validate
 * @param allowedTypes - Allowed MIME types
 * @param maxSize - Maximum file size in bytes
 * @returns Validation result
 */
export const validateFileUpload = (
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  maxSize: number = 5 * 1024 * 1024 // 5MB
): {
  isValid: boolean;
  error?: string;
} => {
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds maximum allowed size ${(maxSize / 1024 / 1024).toFixed(2)}MB`
    };
  }
  
  // Check file name for malicious patterns
  const maliciousPatterns = [
    /\.(exe|bat|cmd|scr|pif|com)$/i,
    /\.\./,
    /[<>:"|?*]/
  ];
  
  for (const pattern of maliciousPatterns) {
    if (pattern.test(file.name)) {
      return {
        isValid: false,
        error: 'File name contains invalid characters'
      };
    }
  }
  
  return {
    isValid: true
  };
};

/**
 * Generate Content Security Policy nonce
 * @returns Random nonce string
 */
export const generateCSPNonce = (): string => {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Escape HTML entities
 * @param text - Text to escape
 * @returns Escaped text
 */
export const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Rate limiting utility for API calls
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 60000 // 1 minute
  ) {}
  
  /**
   * Check if request is allowed
   * @param key - Unique identifier for the request source
   * @returns Whether request is allowed
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
  
  /**
   * Get remaining requests for a key
   * @param key - Unique identifier
   * @returns Number of remaining requests
   */
  getRemainingRequests(key: string): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}

// Export a default rate limiter instance
export const defaultRateLimiter = new RateLimiter();

/**
 * Security headers for API requests
 */
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.gemini.google.com;"
  };
};