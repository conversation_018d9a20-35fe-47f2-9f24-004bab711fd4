import React from 'react';
import { Sparkles, AlertCircle, FileText } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessPlanSection } from '../../services/businessPlanApi';
import ModernRichTextEditor from './ModernRichTextEditor';

interface BusinessPlanSectionEditorProps {
  section: BusinessPlanSection | null;
  content: string;
  onContentChange: (content: string) => void;
  onGenerateWithAI: () => void;
  isGeneratingContent: boolean;
  isSaving: boolean;
  showAutoSaveIndicator: boolean;
  autoSaveError: boolean;
}

/**
 * Business Plan Section Editor Component
 * Handles editing of individual business plan sections
 */
const BusinessPlanSectionEditor: React.FC<BusinessPlanSectionEditorProps> = ({
  section,
  content,
  onContentChange,
  onGenerateWithAI,
  isGeneratingContent,
  isSaving,
  showAutoSaveIndicator,
  autoSaveError
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Show empty state if no section is selected
  if (!section) {
    return (
      <div className="glass-morphism rounded-lg p-6 border border-glass-border text-center">
        <FileText size={40} className="mx-auto mb-3 text-glass-muted" />
        <h3 className="text-lg font-medium mb-2 text-glass-primary">
          {t("businessPlan.noSectionSelected")}
        </h3>
        <div className="text-glass-secondary">
          {t("businessPlan.selectSectionPrompt")}
        </div>
      </div>
    );
  }

  return (
    <div className="business-plan-section-editor">
      {/* Section header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-xl font-semibold text-glass-primary mb-2">
            {section.title}
          </h2>
          {section.description && (
            <p className="text-glass-secondary text-sm">
              {section.description}
            </p>
          )}
        </div>

        {/* AI Generate button */}
        <button
          onClick={onGenerateWithAI}
          disabled={isGeneratingContent || isSaving}
          className={`flex items-center px-4 py-2 rounded-md transition-colors ${
            isGeneratingContent || isSaving
              ? "bg-glass-border text-glass-secondary cursor-not-allowed"
              : "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
          }`}
          aria-label={t("businessPlan.generateWithAI")}
        >
          <Sparkles size={16} className={`mr-2 ${isGeneratingContent ? 'animate-pulse' : ''}`} />
          {isGeneratingContent 
            ? t("businessPlan.generating") 
            : t("businessPlan.generateWithAI")
          }
        </button>
      </div>

      {/* Guiding questions */}
      {section.guiding_questions && section.guiding_questions.length > 0 && (
        <div className="glass-light rounded-md p-4 mb-4 border border-glass-border">
          <h3 className="text-sm font-medium text-glass-primary mb-2 flex items-center">
            <AlertCircle size={16} className="mr-2" />
            {t("businessPlan.guidingQuestions.title")}
          </h3>
          <ul className="space-y-1 text-sm text-glass-secondary">
            {section.guiding_questions.map((question, index) => (
              <li key={index} className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                <span>{question}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Content editor */}
      <div className={`business-plan-editor relative ${isRTL ? 'rtl' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Auto-save indicator */}
        <div className={`auto-save-indicator ${showAutoSaveIndicator ? 'show' : ''} ${autoSaveError ? 'error' : ''} ${isRTL ? 'left-2' : 'right-2'}`}>
          {autoSaveError ? t('businessPlan.autoSaveFailed') : t('businessPlan.autoSaved')}
        </div>

        <ModernRichTextEditor
          value={content}
          onChange={onContentChange}
          placeholder={t("businessPlan.startWriting")}
          disabled={isSaving || isGeneratingContent}
          aria-label={t('businessPlan.editor.sectionContent', { section: section.title })}
          className="w-full"
        />
      </div>

      {/* Section tips */}
      {section.tips && section.tips.length > 0 && (
        <div className="glass-light rounded-md p-4 mt-4 border border-glass-border">
          <h3 className="text-sm font-medium text-glass-primary mb-2">
            {t("businessPlan.tips")}
          </h3>
          <ul className="space-y-1 text-sm text-glass-secondary">
            {section.tips.map((tip, index) => (
              <li key={index} className="flex items-start">
                <span className="text-green-400 mr-2">💡</span>
                <span>{tip}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Word count and character count */}
      <div className="flex justify-between items-center mt-4 text-xs text-glass-secondary">
        <div>
          {t("businessPlan.wordCount")}: {content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length}
        </div>
        <div>
          {t("businessPlan.characterCount")}: {content.replace(/<[^>]*>/g, '').length}
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanSectionEditor;