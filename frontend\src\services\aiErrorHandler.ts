/**
 * AI Error Handler Service
 * Provides graceful error handling and fallback mechanisms for AI services
 */

import { centralizedAiApi } from './centralizedAiApi';

export interface AIErrorContext {
  operation: string;
  contentType?: string;
  context?: Record<string, any>;
  language?: string;
  userId?: number;
}

export interface AIFallbackResponse {
  success: boolean;
  data?: any;
  error?: string;
  isFallback: boolean;
  fallbackReason?: string;
}

/**
 * AI Error Handler Class
 */
class AIErrorHandler {
  private retryAttempts = 3;
  private retryDelay = 1000; // 1 second

  /**
   * Execute AI operation with error handling and fallbacks
   */
  async executeWithFallback<T>(
    operation: () => Promise<T>,
    fallback: () => T,
    context: AIErrorContext
  ): Promise<AIFallbackResponse> {
    let lastError: Error | null = null;

    // Try the main operation with retries
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          data: result,
          isFallback: false
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Log the error
        console.warn(`AI operation failed (attempt ${attempt}/${this.retryAttempts}):`, {
          operation: context.operation,
          error: lastError.message,
          context: context.context
        });

        // Wait before retry (except on last attempt)
        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    // All retries failed, use fallback
    try {
      const fallbackResult = fallback();
      
      console.info(`Using fallback for AI operation: ${context.operation}`, {
        reason: lastError?.message || 'Unknown error',
        context: context.context
      });

      return {
        success: true,
        data: fallbackResult,
        isFallback: true,
        fallbackReason: lastError?.message || 'AI service unavailable'
      };
    } catch (fallbackError) {
      return {
        success: false,
        error: `Both AI operation and fallback failed: ${fallbackError}`,
        isFallback: true,
        fallbackReason: 'Fallback also failed'
      };
    }
  }

  /**
   * Generate business ideas with fallback
   */
  async generateBusinessIdeas(context: {
    industry: string;
    interests: string[];
    skills: string[];
    budget?: string;
    location?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'business_idea_generation',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      return {
        content: JSON.stringify([
          {
            title: `${context.industry} Digital Platform`,
            description: `A digital platform connecting ${context.industry.toLowerCase()} professionals with customers.`,
            industry: context.industry,
            target_market: 'Small to medium businesses',
            revenue_model: 'Commission-based',
            startup_cost: 'medium',
            difficulty: 'intermediate',
            market_potential: 3,
            innovation_score: 3
          },
          {
            title: `AI-Powered ${context.industry} Assistant`,
            description: `An AI assistant for ${context.industry.toLowerCase()} professionals to automate tasks.`,
            industry: context.industry,
            target_market: 'Professionals',
            revenue_model: 'Subscription',
            startup_cost: 'medium',
            difficulty: 'intermediate',
            market_potential: 4,
            innovation_score: 4
          }
        ])
      };
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateBusinessIdeas',
      contentType: 'business_idea_generation',
      context
    });
  }

  /**
   * Generate template recommendations with fallback
   */
  async generateTemplateRecommendations(context: {
    business_idea?: string;
    industry?: string;
    description?: string;
    target_market?: string;
    stage?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'template_recommendations',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      return {
        content: JSON.stringify([
          {
            title: 'Standard Business Plan Template',
            description: 'A comprehensive business plan template suitable for most industries.',
            category: 'good_fit',
            match_score: 0.7,
            reasons: ['Versatile and comprehensive', 'Industry-agnostic'],
            benefits: ['Complete structure', 'Professional format'],
            estimated_time: '3-4 hours'
          },
          {
            title: 'Startup Business Plan Template',
            description: 'A streamlined template focused on startup essentials.',
            category: 'alternative',
            match_score: 0.6,
            reasons: ['Startup-focused', 'Investor-ready format'],
            benefits: ['Concise structure', 'Key metrics focus'],
            estimated_time: '2-3 hours'
          }
        ])
      };
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateTemplateRecommendations',
      contentType: 'template_recommendations',
      context
    });
  }

  /**
   * Generate business plan content with fallback
   */
  async generateBusinessPlanContent(context: {
    section_type: string;
    business_idea?: string;
    existing_content?: string;
    industry?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'business_plan_section',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      const sectionTemplates: Record<string, string> = {
        executive_summary: 'This section should provide a concise overview of your business concept, target market, competitive advantages, and financial projections. Please customize this template with your specific business details.',
        company_description: 'Describe your company\'s mission, vision, values, and the problem you\'re solving. Include information about your business structure, location, and key personnel.',
        market_analysis: 'Analyze your target market, including market size, growth trends, customer segments, and competitive landscape. Include relevant market research and data.',
        products_services: 'Detail your products or services, their features and benefits, pricing strategy, and how they meet customer needs.',
        marketing_sales: 'Outline your marketing strategy, sales process, customer acquisition channels, and promotional activities.',
        financial_projections: 'Present your financial forecasts including revenue projections, expense estimates, cash flow analysis, and break-even analysis.',
        funding_request: 'If seeking funding, specify the amount needed, how funds will be used, and the expected return on investment.',
        appendix: 'Include supporting documents, additional data, charts, and any other relevant information.'
      };

      return {
        content: sectionTemplates[context.section_type] || 'Please provide content for this section based on your business requirements.'
      };
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateBusinessPlanContent',
      contentType: 'business_plan_section',
      context
    });
  }

  /**
   * Check AI service availability
   */
  async checkAvailability(): Promise<boolean> {
    try {
      const status = await centralizedAiApi.getStatus();
      return status.available || false;
    } catch (error) {
      console.warn('AI service availability check failed:', error);
      return false;
    }
  }

  /**
   * Get AI service status with fallback
   */
  async getStatusWithFallback(): Promise<AIFallbackResponse> {
    const operation = async () => {
      return await centralizedAiApi.getStatus();
    };

    const fallback = () => {
      return {
        available: false,
        service: 'fallback',
        features: {
          content_generation: false,
          chat: false,
          business_analysis: false,
          multilingual: false
        },
        error: 'AI service unavailable'
      };
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'getStatus'
    });
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Set retry configuration
   */
  setRetryConfig(attempts: number, delay: number) {
    this.retryAttempts = Math.max(1, attempts);
    this.retryDelay = Math.max(100, delay);
  }
}

// Export singleton instance
export const aiErrorHandler = new AIErrorHandler();

// Export class for testing
export { AIErrorHandler };
