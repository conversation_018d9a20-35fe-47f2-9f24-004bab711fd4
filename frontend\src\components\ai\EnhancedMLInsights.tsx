/**
 * Enhanced ML Insights Component
 * Advanced visualization and analytics for ML predictions
 */

import React from 'react';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  Target,
  BarChart3,
  Activity,
  Zap,
  Shield,
  DollarSign,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface MLInsightsData {
  ml_model_status?: {
    available: boolean;
    initialized: boolean;
    models_loaded: number;
  };
  success_prediction?: {
    probability: number;
    confidence: number;
    key_factors: string[];
    model_type: string;
    model_trained?: boolean;
  };
  market_forecast?: {
    trend: string;
    growth_rate: number;
    volatility: number;
    opportunities: string[];
    risks: string[];
    model_type: string;
  };
  risk_assessment?: {
    overall_risk_score: number;
    risk_level: string;
    top_risks: Array<[string, { score: number; description: string }]>;
    mitigation_strategies: string[];
    model_type: string;
  };
  failure_prediction?: {
    failure_probability: number;
    alert_level: string;
    warning_indicators: string[];
    prevention_recommendations: string[];
    model_type: string;
  };
  investment_readiness?: {
    readiness_score: number;
    readiness_level: string;
    strengths: string[];
    improvement_areas: string[];
    model_type: string;
  };
  summary?: {
    overall_confidence: number;
    key_recommendations: string[];
    risk_level: string;
    success_indicators: string[];
    action_items: string[];
  };
  timestamp?: string;
}

interface EnhancedMLInsightsProps {
  data: MLInsightsData;
  className?: string;
}

export const EnhancedMLInsights: React.FC<EnhancedMLInsightsProps> = ({
  data,
  className = ''
}) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 bg-green-50';
    if (score >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const ProgressBar: React.FC<{ value: number; max?: number; color?: string }> = ({ 
    value, 
    max = 1, 
    color = 'bg-blue-500' 
  }) => (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className={`h-2 rounded-full ${color}`}
        style={{ width: `${Math.min((value / max) * 100, 100)}%` }}
      />
    </div>
  );

  const MetricCard: React.FC<{
    icon: React.ReactNode;
    title: string;
    value: string | number;
    subtitle?: string;
    color?: string;
  }> = ({ icon, title, value, subtitle, color = 'text-blue-600' }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`${color} ${isRTL ? 'ml-2' : 'mr-2'}`}>
          {icon}
        </div>
        <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      </div>
      <div className="text-2xl font-bold text-gray-900 mb-1">
        {typeof value === 'number' ? value.toFixed(3) : value}
      </div>
      {subtitle && (
        <p className="text-xs text-gray-500">{subtitle}</p>
      )}
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* ML Model Status */}
      {data.ml_model_status && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Brain className={`text-purple-600 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'حالة نماذج التعلم الآلي' : 'ML Model Status'}
            </h3>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className={`text-center p-3 rounded-lg ${data.ml_model_status.available ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className={`text-2xl font-bold ${data.ml_model_status.available ? 'text-green-600' : 'text-red-600'}`}>
                {data.ml_model_status.available ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">
                {language === 'ar' ? 'متاح' : 'Available'}
              </div>
            </div>
            <div className={`text-center p-3 rounded-lg ${data.ml_model_status.initialized ? 'bg-green-50' : 'bg-yellow-50'}`}>
              <div className={`text-2xl font-bold ${data.ml_model_status.initialized ? 'text-green-600' : 'text-yellow-600'}`}>
                {data.ml_model_status.initialized ? '✓' : '⚠'}
              </div>
              <div className="text-sm text-gray-600">
                {language === 'ar' ? 'مهيأ' : 'Initialized'}
              </div>
            </div>
            <div className="text-center p-3 rounded-lg bg-blue-50">
              <div className="text-2xl font-bold text-blue-600">
                {data.ml_model_status.models_loaded}
              </div>
              <div className="text-sm text-gray-600">
                {language === 'ar' ? 'النماذج المحملة' : 'Models Loaded'}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {data.success_prediction && (
          <MetricCard
            icon={<Target size={20} />}
            title={language === 'ar' ? 'احتمالية النجاح' : 'Success Probability'}
            value={`${(data.success_prediction.probability * 100).toFixed(1)}%`}
            subtitle={`${language === 'ar' ? 'الثقة:' : 'Confidence:'} ${(data.success_prediction.confidence * 100).toFixed(1)}%`}
            color="text-green-600"
          />
        )}

        {data.risk_assessment && (
          <MetricCard
            icon={<Shield size={20} />}
            title={language === 'ar' ? 'مستوى المخاطر' : 'Risk Level'}
            value={data.risk_assessment.risk_level.toUpperCase()}
            subtitle={`${language === 'ar' ? 'النتيجة:' : 'Score:'} ${(data.risk_assessment.overall_risk_score * 100).toFixed(1)}%`}
            color="text-orange-600"
          />
        )}

        {data.market_forecast && (
          <MetricCard
            icon={<TrendingUp size={20} />}
            title={language === 'ar' ? 'اتجاه السوق' : 'Market Trend'}
            value={data.market_forecast.trend.toUpperCase()}
            subtitle={`${language === 'ar' ? 'النمو:' : 'Growth:'} ${(data.market_forecast.growth_rate * 100).toFixed(1)}%`}
            color="text-blue-600"
          />
        )}

        {data.investment_readiness && (
          <MetricCard
            icon={<DollarSign size={20} />}
            title={language === 'ar' ? 'جاهزية الاستثمار' : 'Investment Readiness'}
            value={`${(data.investment_readiness.readiness_score * 100).toFixed(1)}%`}
            subtitle={data.investment_readiness.readiness_level}
            color="text-purple-600"
          />
        )}
      </div>

      {/* Success Prediction Details */}
      {data.success_prediction && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Target className={`text-green-600 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'تنبؤ النجاح' : 'Success Prediction'}
            </h3>
            {data.success_prediction.model_trained && (
              <span className="ml-auto px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                {language === 'ar' ? 'نموذج مدرب' : 'Trained Model'}
              </span>
            )}
          </div>
          
          <div className="mb-4">
            <div className={`flex justify-between items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span className="text-sm font-medium text-gray-700">
                {language === 'ar' ? 'احتمالية النجاح' : 'Success Probability'}
              </span>
              <span className="text-sm font-bold text-green-600">
                {(data.success_prediction.probability * 100).toFixed(1)}%
              </span>
            </div>
            <ProgressBar 
              value={data.success_prediction.probability} 
              color="bg-green-500" 
            />
          </div>

          <div className="mb-4">
            <div className={`flex justify-between items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span className="text-sm font-medium text-gray-700">
                {language === 'ar' ? 'مستوى الثقة' : 'Confidence Level'}
              </span>
              <span className="text-sm font-bold text-blue-600">
                {(data.success_prediction.confidence * 100).toFixed(1)}%
              </span>
            </div>
            <ProgressBar 
              value={data.success_prediction.confidence} 
              color="bg-blue-500" 
            />
          </div>

          {data.success_prediction.key_factors && data.success_prediction.key_factors.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'العوامل الرئيسية' : 'Key Factors'}
              </h4>
              <div className="space-y-1">
                {data.success_prediction.key_factors.map((factor, index) => (
                  <div key={index} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <CheckCircle className={`text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} size={16} />
                    <span className="text-sm text-gray-600">{factor}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-3 text-xs text-gray-500">
            {language === 'ar' ? 'نوع النموذج:' : 'Model Type:'} {data.success_prediction.model_type}
          </div>
        </div>
      )}

      {/* Risk Assessment */}
      {data.risk_assessment && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <AlertTriangle className={`text-orange-600 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'تقييم المخاطر' : 'Risk Assessment'}
            </h3>
          </div>
          
          <div className="mb-4">
            <div className={`flex justify-between items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span className="text-sm font-medium text-gray-700">
                {language === 'ar' ? 'نتيجة المخاطر الإجمالية' : 'Overall Risk Score'}
              </span>
              <span className={`text-sm font-bold px-2 py-1 rounded ${getRiskColor(data.risk_assessment.risk_level)}`}>
                {data.risk_assessment.risk_level.toUpperCase()}
              </span>
            </div>
            <ProgressBar 
              value={data.risk_assessment.overall_risk_score} 
              color="bg-orange-500" 
            />
          </div>

          {data.risk_assessment.top_risks && data.risk_assessment.top_risks.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'أهم المخاطر' : 'Top Risks'}
              </h4>
              <div className="space-y-2">
                {data.risk_assessment.top_risks.slice(0, 3).map(([riskName, riskData], index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm text-gray-700">{riskName}</span>
                    <span className="text-sm font-medium text-orange-600">
                      {(riskData.score * 100).toFixed(0)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="text-xs text-gray-500">
            {language === 'ar' ? 'نوع النموذج:' : 'Model Type:'} {data.risk_assessment.model_type}
          </div>
        </div>
      )}

      {/* Summary and Recommendations */}
      {data.summary && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <BarChart3 className={`text-blue-600 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'الملخص والتوصيات' : 'Summary & Recommendations'}
            </h3>
          </div>

          <div className="mb-4">
            <div className={`flex justify-between items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span className="text-sm font-medium text-gray-700">
                {language === 'ar' ? 'الثقة الإجمالية' : 'Overall Confidence'}
              </span>
              <span className="text-sm font-bold text-blue-600">
                {(data.summary.overall_confidence * 100).toFixed(1)}%
              </span>
            </div>
            <ProgressBar 
              value={data.summary.overall_confidence} 
              color="bg-blue-500" 
            />
          </div>

          {data.summary.key_recommendations && data.summary.key_recommendations.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'التوصيات الرئيسية' : 'Key Recommendations'}
              </h4>
              <div className="space-y-1">
                {data.summary.key_recommendations.map((recommendation, index) => (
                  <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Zap className={`text-yellow-500 ${isRTL ? 'ml-2' : 'mr-2'} mt-0.5`} size={16} />
                    <span className="text-sm text-gray-600">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Timestamp */}
      {data.timestamp && (
        <div className="text-xs text-gray-500 text-center">
          {language === 'ar' ? 'آخر تحديث:' : 'Last updated:'} {new Date(data.timestamp).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default EnhancedMLInsights;
