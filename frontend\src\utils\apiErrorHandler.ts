/**
 * Centralized API Error Handler
 * Handles authentication errors, token refresh, and Redux state synchronization
 */

import { store } from '../store';
import { clearAuth, getCurrentUser } from '../store/authSlice';
import { authAPI, clearAuthTokens, getAuthToken } from '../services/api';

export interface ApiErrorContext {
  endpoint: string;
  method: string;
  data?: any;
  retryCount?: number;
}

export class ApiErrorHandler {
  private static isRefreshing = false;
  private static refreshPromise: Promise<boolean> | null = null;
  private static pendingRequests: Array<{
    resolve: (value: any) => void;
    reject: (error: any) => void;
    context: ApiErrorContext;
  }> = [];

  /**
   * Handle API errors with automatic token refresh and retry logic
   */
  static async handleApiError(error: any, context: ApiErrorContext): Promise<any> {
    const { status } = error;

    // Handle authentication errors (401)
    if (status === 401) {
      return this.handleAuthenticationError(error, context);
    }

    // Handle other HTTP errors
    if (status >= 400 && status < 500) {
      return this.handleClientError(error, context);
    }

    if (status >= 500) {
      return this.handleServerError(error, context);
    }

    // Handle network errors
    if (!status && error.message?.includes('fetch')) {
      return this.handleNetworkError(error, context);
    }

    // Re-throw unhandled errors
    throw error;
  }

  /**
   * Handle 401 authentication errors with token refresh
   */
  private static async handleAuthenticationError(error: any, context: ApiErrorContext): Promise<any> {
    const token = getAuthToken();
    
    // If no token exists, clear auth state and redirect to login
    if (!token) {
      console.log('🔐 No token found, clearing auth state');
      store.dispatch(clearAuth());
      this.redirectToLogin();
      throw new Error('Authentication required. Please log in.');
    }

    // If already refreshing, queue this request
    if (this.isRefreshing && this.refreshPromise) {
      return this.queueRequest(context);
    }

    // Attempt token refresh
    console.log('🔐 401 error, attempting token refresh');
    return this.attemptTokenRefresh(context);
  }

  /**
   * Attempt to refresh the token and retry the request
   */
  private static async attemptTokenRefresh(context: ApiErrorContext): Promise<any> {
    this.isRefreshing = true;
    this.refreshPromise = authAPI.refreshToken();

    try {
      const refreshSuccess = await this.refreshPromise;

      if (refreshSuccess) {
        console.log('✅ Token refreshed successfully, retrying request');
        
        // Update user data in Redux
        store.dispatch(getCurrentUser());
        
        // Retry the original request
        const result = await this.retryRequest(context);
        
        // Process any queued requests
        this.processQueuedRequests(true);
        
        return result;
      } else {
        console.log('❌ Token refresh failed, clearing auth state');
        this.handleRefreshFailure();
        throw new Error('Session expired. Please log in again.');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      this.handleRefreshFailure();
      throw new Error('Authentication failed. Please log in again.');
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Queue a request while token refresh is in progress
   */
  private static queueRequest(context: ApiErrorContext): Promise<any> {
    return new Promise((resolve, reject) => {
      this.pendingRequests.push({ resolve, reject, context });
    });
  }

  /**
   * Process all queued requests after token refresh
   */
  private static processQueuedRequests(success: boolean) {
    const requests = [...this.pendingRequests];
    this.pendingRequests = [];

    requests.forEach(({ resolve, reject, context }) => {
      if (success) {
        this.retryRequest(context).then(resolve).catch(reject);
      } else {
        reject(new Error('Authentication failed. Please log in again.'));
      }
    });
  }

  /**
   * Handle token refresh failure
   */
  private static handleRefreshFailure() {
    clearAuthTokens();
    store.dispatch(clearAuth());
    this.processQueuedRequests(false);
    this.redirectToLogin();
  }

  /**
   * Retry the original API request
   */
  private static async retryRequest(context: ApiErrorContext): Promise<any> {
    const { endpoint, method, data } = context;
    
    // Dynamically import to avoid circular dependencies
    const { apiRequest } = await import('../services/api');
    
    return apiRequest(endpoint, method, data, true, false); // Don't retry again
  }

  /**
   * Handle client errors (4xx)
   */
  private static handleClientError(error: any, context: ApiErrorContext): never {
    console.error(`Client error ${error.status} for ${context.method} ${context.endpoint}:`, error);
    
    // Add user-friendly error messages
    const userFriendlyMessages: Record<number, string> = {
      400: 'Invalid request. Please check your input.',
      403: 'You do not have permission to perform this action.',
      404: 'The requested resource was not found.',
      409: 'Conflict: The resource already exists or is in use.',
      422: 'Validation error. Please check your input.',
      429: 'Too many requests. Please try again later.'
    };

    const userMessage = userFriendlyMessages[error.status] || error.message;
    throw new Error(userMessage);
  }

  /**
   * Handle server errors (5xx)
   */
  private static handleServerError(error: any, context: ApiErrorContext): never {
    console.error(`Server error ${error.status} for ${context.method} ${context.endpoint}:`, error);
    
    const userMessage = error.status === 503 
      ? 'Service temporarily unavailable. Please try again later.'
      : 'Server error. Please try again later.';
      
    throw new Error(userMessage);
  }

  /**
   * Handle network errors
   */
  private static handleNetworkError(error: any, context: ApiErrorContext): never {
    console.error(`Network error for ${context.method} ${context.endpoint}:`, error);
    throw new Error('Network error. Please check your internet connection.');
  }

  /**
   * Redirect to login page
   */
  private static redirectToLogin() {
    const currentPath = window.location.pathname;
    const returnUrl = currentPath !== '/login' ? `?returnUrl=${encodeURIComponent(currentPath)}` : '';
    window.location.href = `/login${returnUrl}`;
  }

  /**
   * Check if an error should be handled by this handler
   */
  static shouldHandle(error: any): boolean {
    return error && (
      typeof error.status === 'number' ||
      error.message?.includes('fetch') ||
      error.message?.includes('Authentication')
    );
  }
}

/**
 * Enhanced API request wrapper with error handling
 */
export async function apiRequestWithErrorHandling<T>(
  endpoint: string,
  method: string = 'GET',
  data?: any,
  retryCount: number = 0
): Promise<T> {
  const context: ApiErrorContext = { endpoint, method, data, retryCount };

  try {
    // Dynamically import to avoid circular dependencies
    const { apiRequest } = await import('../services/api');
    return await apiRequest<T>(endpoint, method, data);
  } catch (error) {
    if (ApiErrorHandler.shouldHandle(error)) {
      return ApiErrorHandler.handleApiError(error, context);
    }
    throw error;
  }
}

export default ApiErrorHandler;
