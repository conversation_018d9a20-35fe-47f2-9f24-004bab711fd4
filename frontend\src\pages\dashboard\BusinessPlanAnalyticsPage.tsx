import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  BarChart2, 
  Clock, 
  Users, 
  Download,
  TrendingUp,
  Target,
  Activity,
  Calendar,
  FileText,
  Eye,
  Edit,
  Share2
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { businessPlansAPI, BusinessPlan } from '../../services/businessPlanApi';
import { businessPlanAnalyticsAPI } from '../../services/businessPlanAnalyticsApi';
import { LoadingFallback } from '../../components/ui';

interface AnalyticsData {
  timeSpent: {
    total: number;
    sessions: number;
    average: number;
    lastActivity: string;
  };
  progress: {
    completion: number;
    sectionsCompleted: number;
    totalSections: number;
    wordCount: number;
  };
  collaboration: {
    collaborators: number;
    comments: number;
    revisions: number;
  };
  exports: {
    totalExports: number;
    lastExport: string;
    formats: { [key: string]: number };
  };
}

const BusinessPlanAnalyticsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useAppSelector((state) => state.language);
  
  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Fetch business plan
        const plan = await businessPlansAPI.getPlan(parseInt(id));
        setBusinessPlan(plan);

        // Fetch real analytics data from API
        try {
          const [timeAnalytics, collaborationAnalytics, exportStats] = await Promise.all([
            businessPlanAnalyticsAPI.getTimeAnalytics({
              business_plan_id: parseInt(id),
              date_range: timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
            }),
            businessPlanAnalyticsAPI.getCollaborationStats({
              business_plan_id: parseInt(id),
              date_range: timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
            }),
            businessPlanAnalyticsAPI.getExportStats({
              business_plan_id: parseInt(id),
              date_range: timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
            })
          ]);

          const realAnalytics: AnalyticsData = {
            timeSpent: {
              total: Math.round((timeAnalytics.total_time_seconds || 0) / 60), // Convert seconds to minutes
              sessions: timeAnalytics.total_sessions || 0,
              average: Math.round((timeAnalytics.average_session_duration || 0) / 60), // Convert seconds to minutes
              lastActivity: timeAnalytics.daily_breakdown?.[0]?.day || new Date().toISOString()
            },
            progress: {
              completion: plan.completion_percentage || 0,
              sectionsCompleted: plan.sections?.filter(s => s.content && s.content.trim().length > 0).length || 0,
              totalSections: plan.sections?.length || 0,
              wordCount: plan.sections?.reduce((total, section) => {
                const text = section.content?.replace(/<[^>]*>/g, '') || '';
                return total + text.split(/\s+/).filter(word => word.length > 0).length;
              }, 0) || 0
            },
            collaboration: {
              collaborators: collaborationAnalytics.unique_collaborators || 1,
              comments: collaborationAnalytics.action_breakdown?.find(a => a.action_type === 'comment')?.count || 0,
              revisions: collaborationAnalytics.action_breakdown?.find(a => a.action_type === 'edit')?.count || 0
            },
            exports: {
              totalExports: exportStats.total_exports || 0,
              lastExport: exportStats.daily_exports?.[0]?.day || new Date().toISOString(),
              formats: exportStats.format_breakdown?.reduce((acc, item) => {
                acc[item.export_format] = item.count;
                return acc;
              }, {} as { [key: string]: number }) || {}
            }
          };

          setAnalytics(realAnalytics);
        } catch (analyticsError) {
          console.error('Error fetching analytics data:', analyticsError);

          // Fallback to calculated analytics from business plan data
          const fallbackAnalytics: AnalyticsData = {
            timeSpent: {
              total: 0,
              sessions: 0,
              average: 0,
              lastActivity: plan.updated_at || new Date().toISOString()
            },
            progress: {
              completion: plan.completion_percentage || 0,
              sectionsCompleted: plan.sections?.filter(s => s.content && s.content.trim().length > 0).length || 0,
              totalSections: plan.sections?.length || 0,
              wordCount: plan.sections?.reduce((total, section) => {
                const text = section.content?.replace(/<[^>]*>/g, '') || '';
                return total + text.split(/\s+/).filter(word => word.length > 0).length;
              }, 0) || 0
            },
            collaboration: {
              collaborators: 1,
              comments: 0,
              revisions: 0
            },
            exports: {
              totalExports: 0,
              lastExport: '',
              formats: {}
            }
          };

          setAnalytics(fallbackAnalytics);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, timeRange]);

  const handleBack = () => {
    navigate(`/dashboard/business-plans/${id}`);
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}${t('businessPlan.analytics.hours')} ${mins}${t('businessPlan.analytics.minutes')}`;
    }
    return `${mins}${t('businessPlan.analytics.minutes')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <LoadingFallback message={t('businessPlan.analytics.loading')} />
      </div>
    );
  }

  if (error || !businessPlan || !analytics) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center">
          <BarChart2 className="mx-auto mb-4 text-red-400" size={48} />
          <h2 className="text-xl font-semibold text-white mb-2">
            {t('businessPlan.analytics.error')}
          </h2>
          <p className="text-gray-400 mb-4">
            {error || t('businessPlan.analytics.errorLoading')}
          </p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.back')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between py-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={handleBack}
                className={`flex items-center text-gray-300 hover:text-white transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <ArrowLeft size={20} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('businessPlan.backToEditor')}
              </button>
            </div>
            
            <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
                className="bg-white/10 border border-white/20 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="7d">{t('businessPlan.analytics.last7Days')}</option>
                <option value="30d">{t('businessPlan.analytics.last30Days')}</option>
                <option value="90d">{t('businessPlan.analytics.last90Days')}</option>
                <option value="1y">{t('businessPlan.analytics.lastYear')}</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            {t('businessPlan.analytics.businessPlanAnalytics')}
          </h1>
          <p className="text-gray-400">
            {businessPlan.title}
          </p>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Time Spent */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Clock className="text-blue-400" size={24} />
              <span className="text-2xl font-bold text-white">
                {formatTime(analytics.timeSpent.total)}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {t('businessPlan.analytics.timeSpent')}
            </h3>
            <p className="text-gray-400 text-sm">
              {analytics.timeSpent.sessions} {t('businessPlan.analytics.sessionsCount')}
            </p>
          </div>

          {/* Progress */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Target className="text-green-400" size={24} />
              <span className="text-2xl font-bold text-white">
                {analytics.progress.completion}%
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {t('businessPlan.analytics.completion')}
            </h3>
            <p className="text-gray-400 text-sm">
              {analytics.progress.sectionsCompleted}/{analytics.progress.totalSections} {t('businessPlan.sections')}
            </p>
          </div>

          {/* Collaborators */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Users className="text-purple-400" size={24} />
              <span className="text-2xl font-bold text-white">
                {analytics.collaboration.collaborators}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {t('businessPlan.analytics.collaborators')}
            </h3>
            <p className="text-gray-400 text-sm">
              {analytics.collaboration.revisions} {t('businessPlan.analytics.revisions')}
            </p>
          </div>

          {/* Exports */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Download className="text-orange-400" size={24} />
              <span className="text-2xl font-bold text-white">
                {analytics.exports.totalExports}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {t('businessPlan.analytics.exports')}
            </h3>
            <p className="text-gray-400 text-sm">
              {t('businessPlan.analytics.lastExport')}: {new Date(analytics.exports.lastExport).toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Progress Details */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <TrendingUp className={`text-green-400 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
              {t('businessPlan.analytics.yourProgress')}
            </h3>
            
            <div className="space-y-4">
              <div>
                <div className={`flex justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-300">{t('businessPlan.analytics.completion')}</span>
                  <span className="text-white font-semibold">{analytics.progress.completion}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${analytics.progress.completion}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{analytics.progress.wordCount}</div>
                  <div className="text-gray-400 text-sm">{t('businessPlan.wordCount')}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{analytics.progress.sectionsCompleted}</div>
                  <div className="text-gray-400 text-sm">{t('businessPlan.sectionsComplete')}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Activity Timeline */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Activity className={`text-blue-400 ${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
              {t('businessPlan.analytics.lastActivity')}
            </h3>
            
            <div className="space-y-3">
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="text-white text-sm">{t('businessPlan.analytics.lastActivity')}</div>
                  <div className="text-gray-400 text-xs">
                    {new Date(analytics.timeSpent.lastActivity).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="text-white text-sm">{t('businessPlan.analytics.averageTime')}</div>
                  <div className="text-gray-400 text-xs">
                    {formatTime(analytics.timeSpent.average)} {t('businessPlan.analytics.perSession')}
                  </div>
                </div>
              </div>
              
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="text-white text-sm">{t('businessPlan.analytics.totalTime')}</div>
                  <div className="text-gray-400 text-xs">
                    {formatTime(analytics.timeSpent.total)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanAnalyticsPage;
