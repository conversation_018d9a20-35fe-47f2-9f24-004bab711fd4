import React, { useEffect, useCallback, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Save, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { 
  useBusinessPlanDetail,
  useBusinessPlanSections,
  useBusinessPlanSectionEditor,
  useAutoSave
} from '../../hooks/useBusinessPlansRedux';
import { businessPlanSyncService } from '../../services/businessPlanSyncService';
import { useLanguage } from '../../hooks/useLanguage';
import BusinessPlanHeader from './BusinessPlanHeader';
import BusinessPlanSectionList from './BusinessPlanSectionList';
import BusinessPlanSectionEditor from './BusinessPlanSectionEditor';
import BusinessPlanErrorBoundary from './BusinessPlanErrorBoundary';

interface EnhancedBusinessPlanEditorProps {
  businessPlanId?: number;
}

/**
 * Enhanced Business Plan Editor with improved state management and synchronization
 */
const EnhancedBusinessPlanEditor: React.FC<EnhancedBusinessPlanEditorProps> = ({
  businessPlanId: propBusinessPlanId
}) => {
  const { id: paramId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Determine business plan ID from props or params
  const businessPlanId = propBusinessPlanId || (paramId ? parseInt(paramId, 10) : null);

  // Redux hooks for business plan management
  const { 
    businessPlan, 
    isLoading: planLoading, 
    error: planError,
    fetchPlan,
    updatePlan 
  } = useBusinessPlanDetail(businessPlanId || undefined);

  const {
    sections,
    isLoading: sectionsLoading,
    error: sectionsError,
    fetchSections
  } = useBusinessPlanSections(businessPlanId || undefined);

  const {
    activeSectionId,
    currentSection,
    sectionContent,
    hasUnsavedChanges,
    isAutoSaving,
    autoSaveError,
    setActiveSectionId,
    setSectionContent,
    saveSection,
    generateContent
  } = useBusinessPlanSectionEditor();

  // Enhanced auto-save with race condition prevention
  const { setManualSaveInProgress } = useAutoSave(
    activeSectionId, 
    businessPlanId, 
    10000 // 10 second delay
  );

  // Local state for UI
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncError, setSyncError] = useState<string | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Computed values
  const loading = planLoading || sectionsLoading;
  const error = planError || sectionsError;

  // Initialize data
  useEffect(() => {
    if (!businessPlanId) {
      navigate('/dashboard/business-plans');
      return;
    }

    // Initial data fetch
    if (!businessPlan) {
      fetchPlan(businessPlanId);
    }
    if (!sections.length) {
      fetchSections(businessPlanId);
    }
  }, [businessPlanId, businessPlan, sections.length, fetchPlan, fetchSections, navigate]);

  // Handle manual save with conflict resolution
  const handleSaveSection = useCallback(async () => {
    if (!activeSectionId || !businessPlanId) return;

    try {
      // Prevent auto-save during manual save
      setManualSaveInProgress(true);

      // Check for conflicts before saving
      const conflictCheck = await businessPlanSyncService.handleAutoSaveConflict(
        activeSectionId,
        sectionContent
      );

      if (conflictCheck.hasConflict) {
        // Handle conflict - in a real app, you might show a merge dialog
        const userChoice = window.confirm(
          t('businessPlan.conflictDetected', 'Content has been modified by another user. Overwrite?')
        );
        
        if (!userChoice) {
          return;
        }
      }

      // Perform the save
      await saveSection(activeSectionId, sectionContent);

      // Sync the updated section
      await businessPlanSyncService.syncBusinessPlanSection(activeSectionId);

      // Update business plan completion
      await businessPlanSyncService.syncBusinessPlan(businessPlanId);

    } catch (error) {
      console.error('Error saving section:', error);
    } finally {
      setManualSaveInProgress(false);
    }
  }, [
    activeSectionId, 
    businessPlanId, 
    sectionContent, 
    saveSection, 
    setManualSaveInProgress,
    t
  ]);

  // Handle content generation
  const handleGenerateContent = useCallback(async () => {
    if (!activeSectionId || !businessPlanId) return;

    try {
      setManualSaveInProgress(true);
      await generateContent(activeSectionId);
      
      // Sync after generation
      await businessPlanSyncService.syncBusinessPlanSection(activeSectionId);
      await businessPlanSyncService.syncBusinessPlan(businessPlanId);
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setManualSaveInProgress(false);
    }
  }, [activeSectionId, businessPlanId, generateContent, setManualSaveInProgress]);

  // Handle manual sync
  const handleManualSync = useCallback(async () => {
    if (!businessPlanId) return;

    try {
      setIsSyncing(true);
      setSyncError(null);

      // Force sync all data
      await businessPlanSyncService.syncBusinessPlan(businessPlanId, { forceRefresh: true });
      await businessPlanSyncService.syncBusinessPlanSections(businessPlanId, { forceRefresh: true });

      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Manual sync failed:', error);
      setSyncError(error instanceof Error ? error.message : 'Sync failed');
    } finally {
      setIsSyncing(false);
    }
  }, [businessPlanId]);

  // Handle section selection
  const handleSectionSelect = useCallback((sectionId: number) => {
    // Warn about unsaved changes
    if (hasUnsavedChanges) {
      const shouldContinue = window.confirm(
        t('businessPlan.unsavedChangesWarning', 'You have unsaved changes. Continue?')
      );
      if (!shouldContinue) return;
    }

    setActiveSectionId(sectionId);
  }, [hasUnsavedChanges, setActiveSectionId, t]);

  // Handle content change
  const handleContentChange = useCallback((content: string) => {
    setSectionContent(content);
  }, [setSectionContent]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">{t('businessPlan.loadingPlan')}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold mb-2 text-gray-900">
            {t('businessPlan.loadError')}
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary"
          >
            {t('common.retry')}
          </button>
        </div>
      </div>
    );
  }

  if (!businessPlan) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">{t('businessPlan.planNotFound')}</p>
        </div>
      </div>
    );
  }

  return (
    <BusinessPlanErrorBoundary>
      <div className={`business-plan-editor-container ${isRTL ? 'rtl' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Header with sync controls */}
        <BusinessPlanHeader 
          businessPlan={businessPlan}
          onSync={handleManualSync}
          isSyncing={isSyncing}
          syncError={syncError}
          lastSyncTime={lastSyncTime}
        />

        <div className="flex flex-1 overflow-hidden">
          {/* Sections sidebar */}
          <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
            <BusinessPlanSectionList
              sections={sections}
              activeSectionId={activeSectionId}
              onSectionSelect={handleSectionSelect}
              hasUnsavedChanges={hasUnsavedChanges}
            />
          </div>

          {/* Section editor */}
          <div className="flex-1 flex flex-col">
            {currentSection ? (
              <BusinessPlanSectionEditor
                section={currentSection}
                content={sectionContent}
                onContentChange={handleContentChange}
                onSave={handleSaveSection}
                onGenerateContent={handleGenerateContent}
                hasUnsavedChanges={hasUnsavedChanges}
                isAutoSaving={isAutoSaving}
                autoSaveError={autoSaveError}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <p>{t('businessPlan.selectSectionPrompt')}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status indicators */}
        <div className="fixed bottom-4 right-4 space-y-2">
          {isAutoSaving && (
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
              <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              {t('businessPlan.autoSaving')}
            </div>
          )}
          
          {autoSaveError && (
            <div className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm flex items-center">
              <AlertTriangle className="w-4 h-4 mr-2" />
              {t('businessPlan.autoSaveFailed')}
            </div>
          )}
          
          {lastSyncTime && (
            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center">
              <CheckCircle className="w-4 h-4 mr-2" />
              {t('businessPlan.lastSynced')}: {lastSyncTime.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>
    </BusinessPlanErrorBoundary>
  );
};

export default EnhancedBusinessPlanEditor;
