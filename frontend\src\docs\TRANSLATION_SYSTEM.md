# 🌍 Translation System Documentation

## Overview

The Yasmeen AI application uses a comprehensive internationalization (i18n) system supporting English and Arabic languages with full RTL (Right-to-Left) support.

## 🏗️ Architecture

### Core Components

1. **i18next** - Main internationalization framework
2. **react-i18next** - React integration
3. **Modular Translation Files** - Organized by feature/section
4. **RTL Support** - Complete right-to-left layout support
5. **Translation Validation** - Development tools for quality assurance

### File Structure

```
src/
├── i18n/
│   └── index.ts                 # Main i18n configuration
├── locales/
│   ├── en/                      # English translations
│   │   ├── common.json
│   │   ├── ai.json
│   │   ├── dashboard.json
│   │   ├── businessPlan.json
│   │   └── ...
│   └── ar/                      # Arabic translations
│       ├── common.json
│       ├── ai.json
│       ├── dashboard.json
│       ├── businessPlan.json
│       └── ...
├── components/dev/
│   └── TranslationValidator.tsx # Development validation tool
└── utils/
    └── translationUtils.ts     # Translation utilities
```

## 🔧 Configuration

### Main Configuration (i18n/index.ts)

```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import all translation modules
import enCommon from '../locales/en/common.json';
import arCommon from '../locales/ar/common.json';
// ... other imports

const resources = {
  en: {
    translation: mergeTranslations(
      enCommon,
      enAI,
      enDashboard,
      // ... other modules
    )
  },
  ar: {
    translation: mergeTranslations(
      arCommon,
      arAI,
      arDashboard,
      // ... other modules
    )
  }
};
```

## 📝 Translation Key Structure

### Naming Convention

Use **hierarchical, descriptive keys** with dot notation:

```json
{
  "dashboard": {
    "analytics": {
      "title": "Analytics",
      "timeSpent": "Time Spent",
      "exportStatistics": "Export Statistics"
    },
    "noIdeas": {
      "ai": {
        "title": "AI Assistant",
        "description": "Get personalized guidance"
      }
    }
  }
}
```

### Best Practices

1. **Descriptive Keys**: Use meaningful, self-documenting key names
2. **Hierarchical Structure**: Group related translations logically
3. **Consistent Naming**: Follow the same pattern across all files
4. **Avoid Hardcoded Text**: Always use translation keys in components

## 🚀 Usage in Components

### Basic Usage

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('dashboard.analytics.title')}</h1>
      <p>{t('dashboard.analytics.description')}</p>
    </div>
  );
};
```

### With Fallback Text

```tsx
const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <h1>{t('dashboard.title', 'Dashboard')}</h1>
  );
};
```

### With Variables

```tsx
const MyComponent = ({ userName }) => {
  const { t } = useTranslation();
  
  return (
    <p>{t('welcome.message', { name: userName })}</p>
  );
};
```

## 🔍 Translation Validation

### Development Validator

The `TranslationValidator` component helps identify missing translations during development:

```tsx
import TranslationValidator from './components/dev/TranslationValidator';

// Automatically enabled in development mode
<TranslationValidator />
```

### Manual Validation Script

Run the translation audit script:

```bash
node src/scripts/fix-translations.js
```

### Translation Utilities

```typescript
import { 
  validateTranslations,
  findMissingKeys,
  mergeTranslations 
} from './utils/translationUtils';

// Validate translation completeness
const validation = validateTranslations(enTranslations, arTranslations);

// Find missing keys
const missing = findMissingKeys(sourceTranslations, targetTranslations);
```

## 🌐 RTL Support

### Language Detection

```tsx
import { useLanguage } from './hooks/useLanguage';

const MyComponent = () => {
  const { isRTL, language } = useLanguage();
  
  return (
    <div className={isRTL ? 'text-right' : 'text-left'}>
      Content adapts to language direction
    </div>
  );
};
```

### RTL Components

Use specialized RTL-aware components:

```tsx
import { RTLText, RTLContainer, RTLFlex } from './components/rtl';

<RTLContainer textAlign="start" padding={{ start: 2, end: 1 }}>
  <RTLText align="start">This text respects RTL direction</RTLText>
</RTLContainer>
```

## 🔧 Adding New Translations

### 1. Add to Translation Files

**English (en/feature.json):**
```json
{
  "newFeature": {
    "title": "New Feature",
    "description": "Feature description",
    "actions": {
      "save": "Save",
      "cancel": "Cancel"
    }
  }
}
```

**Arabic (ar/feature.json):**
```json
{
  "newFeature": {
    "title": "ميزة جديدة",
    "description": "وصف الميزة",
    "actions": {
      "save": "حفظ",
      "cancel": "إلغاء"
    }
  }
}
```

### 2. Import in i18n Configuration

```typescript
import enFeature from '../locales/en/feature.json';
import arFeature from '../locales/ar/feature.json';

const resources = {
  en: {
    translation: mergeTranslations(
      // ... existing imports
      enFeature
    )
  },
  ar: {
    translation: mergeTranslations(
      // ... existing imports
      arFeature
    )
  }
};
```

### 3. Use in Components

```tsx
const { t } = useTranslation();
return <h1>{t('newFeature.title')}</h1>;
```

## 🐛 Common Issues & Solutions

### Issue: Translation Key Not Found

**Problem:** Translation returns the key itself instead of translated text.

**Solution:**
1. Check if the key exists in both language files
2. Verify the key path is correct
3. Ensure the translation file is imported in i18n configuration

### Issue: RTL Layout Problems

**Problem:** Text or layout doesn't adapt properly to Arabic.

**Solution:**
1. Use RTL-aware components
2. Apply conditional CSS classes based on `isRTL`
3. Test with Arabic language enabled

### Issue: Missing Translations in Production

**Problem:** Some translations work in development but not in production.

**Solution:**
1. Run the translation validation script
2. Check for missing keys in both language files
3. Verify all translation files are properly imported

## 📊 Translation Coverage

Current translation coverage:

- **English**: 100% (base language)
- **Arabic**: ~95% (some new keys may be missing)

### Key Areas Covered

- ✅ Authentication & Authorization
- ✅ Dashboard & Analytics  
- ✅ AI Features & Chat
- ✅ Business Plan Management
- ✅ User Interface Elements
- ✅ Error Messages & Notifications
- ✅ Navigation & Menus

## 🔄 Maintenance

### Regular Tasks

1. **Run Translation Audit**: Weekly validation of translation completeness
2. **Update Missing Keys**: Add translations for new features
3. **Review RTL Layout**: Test Arabic layout with new components
4. **Performance Check**: Monitor translation loading performance

### Tools Available

- `TranslationValidator` - Real-time development validation
- `fix-translations.js` - Automated fixing script
- `translation-audit.js` - Comprehensive audit tool
- `translationUtils.ts` - Utility functions for validation

## 🎯 Best Practices Summary

1. **Always use translation keys** - Never hardcode text
2. **Provide fallback text** - Include default English text
3. **Test both languages** - Verify functionality in English and Arabic
4. **Use hierarchical keys** - Organize translations logically
5. **Validate regularly** - Run audit scripts frequently
6. **Consider RTL layout** - Design with Arabic users in mind
7. **Document new keys** - Update this documentation when adding features
