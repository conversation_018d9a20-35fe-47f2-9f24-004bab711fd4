#!/usr/bin/env python
"""
Create sample business plan templates for testing
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from incubator.models_business_plan import BusinessPlanTemplate

def create_sample_templates():
    """Create sample business plan templates"""
    
    templates = [
        {
            'name': 'Technology Startup Template',
            'description': 'Comprehensive business plan template for technology startups',
            'industry': 'technology',
            'template_type': 'startup',
            'difficulty_level': 'intermediate',
            'estimated_time': 45,
            'sections': {
                'executive_summary': {
                    'title': 'Executive Summary',
                    'description': 'Brief overview of your technology startup',
                    'required': True,
                    'order': 1,
                    'guiding_questions': [
                        'What is your technology startup concept?',
                        'What problem does your technology solve?',
                        'Who is your target market?',
                        'What makes your solution unique?',
                        'What are your key success metrics?'
                    ],
                    'ai_prompt': 'Generate a compelling executive summary for a technology startup focusing on innovation, market opportunity, and competitive advantage.'
                },
                'market_analysis': {
                    'title': 'Market Analysis',
                    'description': 'Analysis of your target market and competition',
                    'required': True,
                    'order': 2,
                    'guiding_questions': [
                        'What is the size of your target market?',
                        'Who are your main competitors?',
                        'What are the market trends?',
                        'What is your competitive advantage?',
                        'How will you position your product?'
                    ],
                    'ai_prompt': 'Analyze the technology market including size, trends, competition, and opportunities for this startup.'
                },
                'product_development': {
                    'title': 'Product Development',
                    'description': 'Technical specifications and development roadmap',
                    'required': True,
                    'order': 3,
                    'guiding_questions': [
                        'What are the key features of your product?',
                        'What technology stack will you use?',
                        'What is your development timeline?',
                        'What are the technical challenges?',
                        'How will you ensure product quality?'
                    ],
                    'ai_prompt': 'Outline a comprehensive product development plan including technical specifications, timeline, and quality assurance.'
                },
                'financial_projections': {
                    'title': 'Financial Projections',
                    'description': 'Revenue forecasts and funding requirements',
                    'required': True,
                    'order': 4,
                    'guiding_questions': [
                        'What are your revenue projections?',
                        'What are your main cost drivers?',
                        'How much funding do you need?',
                        'What is your path to profitability?',
                        'What are your key financial assumptions?'
                    ],
                    'ai_prompt': 'Create detailed financial projections including revenue forecasts, cost structure, and funding requirements for a technology startup.'
                }
            },
            'usage_count': 150,
            'completion_rate': 85.5,
            'is_premium': False
        },
        {
            'name': 'E-commerce Business Plan',
            'description': 'Complete template for online retail businesses',
            'industry': 'ecommerce',
            'template_type': 'ecommerce',
            'difficulty_level': 'beginner',
            'estimated_time': 30,
            'sections': {
                'business_overview': {
                    'title': 'Business Overview',
                    'description': 'Overview of your e-commerce business',
                    'required': True,
                    'order': 1,
                    'guiding_questions': [
                        'What products will you sell online?',
                        'What is your unique value proposition?',
                        'Who is your target customer?',
                        'What is your business model?',
                        'What are your main goals?'
                    ],
                    'ai_prompt': 'Create a comprehensive business overview for an e-commerce store focusing on products, target market, and value proposition.'
                },
                'product_catalog': {
                    'title': 'Product Catalog',
                    'description': 'Description of products you will sell',
                    'required': True,
                    'order': 2,
                    'guiding_questions': [
                        'What products will you offer?',
                        'How will you source your products?',
                        'What are your pricing strategies?',
                        'How will you manage inventory?',
                        'What are your product categories?'
                    ],
                    'ai_prompt': 'Develop a detailed product catalog including sourcing, pricing, and inventory management strategies.'
                },
                'marketing_strategy': {
                    'title': 'Marketing Strategy',
                    'description': 'How you will attract and retain customers',
                    'required': True,
                    'order': 3,
                    'guiding_questions': [
                        'How will you attract customers?',
                        'What marketing channels will you use?',
                        'What is your customer acquisition cost?',
                        'How will you retain customers?',
                        'What is your brand positioning?'
                    ],
                    'ai_prompt': 'Create a comprehensive marketing strategy for e-commerce including digital marketing, customer acquisition, and retention.'
                },
                'operations_plan': {
                    'title': 'Operations Plan',
                    'description': 'Logistics, fulfillment, and customer service',
                    'required': True,
                    'order': 4,
                    'guiding_questions': [
                        'How will you handle order fulfillment?',
                        'What is your shipping strategy?',
                        'How will you handle returns?',
                        'What customer service will you provide?',
                        'What technology platforms will you use?'
                    ],
                    'ai_prompt': 'Outline comprehensive operations including fulfillment, shipping, customer service, and technology infrastructure.'
                }
            },
            'usage_count': 200,
            'completion_rate': 78.2,
            'is_premium': False
        },
        {
            'name': 'Restaurant Business Plan',
            'description': 'Specialized template for restaurant and food service businesses',
            'industry': 'restaurant',
            'template_type': 'restaurant',
            'difficulty_level': 'intermediate',
            'estimated_time': 60,
            'sections': {
                'concept_overview': {
                    'title': 'Restaurant Concept',
                    'description': 'Your restaurant concept and unique value proposition',
                    'required': True,
                    'order': 1,
                    'guiding_questions': [
                        'What type of restaurant are you opening?',
                        'What is your unique concept?',
                        'Who is your target customer?',
                        'What atmosphere will you create?',
                        'What makes you different from competitors?'
                    ],
                    'ai_prompt': 'Develop a compelling restaurant concept including theme, target market, and unique value proposition.'
                },
                'menu_planning': {
                    'title': 'Menu Planning',
                    'description': 'Menu items, pricing, and food costs',
                    'required': True,
                    'order': 2,
                    'guiding_questions': [
                        'What type of cuisine will you serve?',
                        'What are your signature dishes?',
                        'How will you price your menu items?',
                        'What are your food costs?',
                        'How will you source ingredients?'
                    ],
                    'ai_prompt': 'Create a comprehensive menu plan including dishes, pricing strategy, and cost analysis.'
                },
                'location_analysis': {
                    'title': 'Location Analysis',
                    'description': 'Site selection and demographic analysis',
                    'required': True,
                    'order': 3,
                    'guiding_questions': [
                        'Where will your restaurant be located?',
                        'What are the demographics of the area?',
                        'What is the foot traffic like?',
                        'Who are your local competitors?',
                        'What are the lease terms and costs?'
                    ],
                    'ai_prompt': 'Analyze potential restaurant locations including demographics, competition, and site selection criteria.'
                },
                'financial_planning': {
                    'title': 'Financial Planning',
                    'description': 'Startup costs, operating expenses, and revenue projections',
                    'required': True,
                    'order': 4,
                    'guiding_questions': [
                        'What are your startup costs?',
                        'What are your monthly operating expenses?',
                        'What are your revenue projections?',
                        'How much funding do you need?',
                        'What is your break-even point?'
                    ],
                    'ai_prompt': 'Develop detailed financial projections including startup costs, operating expenses, and revenue forecasts for a restaurant.'
                }
            },
            'usage_count': 95,
            'completion_rate': 72.8,
            'is_premium': False
        },
        {
            'name': 'Consulting Services Template',
            'description': 'Professional template for consulting and service businesses',
            'industry': 'consulting',
            'template_type': 'consulting',
            'difficulty_level': 'beginner',
            'estimated_time': 25,
            'sections': {
                'service_overview': {
                    'title': 'Service Overview',
                    'description': 'Description of your consulting services',
                    'required': True,
                    'order': 1,
                    'guiding_questions': [
                        'What consulting services do you offer?',
                        'What is your area of expertise?',
                        'What problems do you solve for clients?',
                        'What is your unique approach?',
                        'What results do you deliver?'
                    ],
                    'ai_prompt': 'Describe comprehensive consulting services including expertise, approach, and value delivered to clients.'
                },
                'target_market': {
                    'title': 'Target Market',
                    'description': 'Identification of your ideal clients',
                    'required': True,
                    'order': 2,
                    'guiding_questions': [
                        'Who are your ideal clients?',
                        'What industries do you serve?',
                        'What size companies do you target?',
                        'What are their main challenges?',
                        'How do you reach them?'
                    ],
                    'ai_prompt': 'Define the target market for consulting services including client profiles, industries, and market approach.'
                },
                'pricing_strategy': {
                    'title': 'Pricing Strategy',
                    'description': 'How you will price your services',
                    'required': True,
                    'order': 3,
                    'guiding_questions': [
                        'How will you price your services?',
                        'Will you charge hourly or project-based?',
                        'What are your rates compared to competitors?',
                        'How will you justify your pricing?',
                        'What payment terms will you offer?'
                    ],
                    'ai_prompt': 'Develop a pricing strategy for consulting services including rate structure, competitive analysis, and payment terms.'
                },
                'growth_plan': {
                    'title': 'Growth Plan',
                    'description': 'How you will scale your consulting business',
                    'required': True,
                    'order': 4,
                    'guiding_questions': [
                        'How will you grow your consulting business?',
                        'Will you hire additional consultants?',
                        'How will you expand your service offerings?',
                        'What marketing strategies will you use?',
                        'What are your revenue goals?'
                    ],
                    'ai_prompt': 'Create a growth plan for scaling a consulting business including team expansion, service development, and marketing strategies.'
                }
            },
            'usage_count': 120,
            'completion_rate': 88.1,
            'is_premium': False
        },
        {
            'name': 'SaaS Startup Template',
            'description': 'Comprehensive template for Software as a Service businesses',
            'industry': 'saas',
            'template_type': 'saas',
            'difficulty_level': 'advanced',
            'estimated_time': 90,
            'sections': {
                'product_vision': {
                    'title': 'Product Vision',
                    'description': 'Your SaaS product vision and mission',
                    'required': True,
                    'order': 1,
                    'guiding_questions': [
                        'What is your SaaS product vision?',
                        'What problem does your software solve?',
                        'Who is your target user?',
                        'What is your product mission?',
                        'How will you measure success?'
                    ],
                    'ai_prompt': 'Articulate a compelling product vision for a SaaS startup including problem statement, target users, and success metrics.'
                },
                'technical_architecture': {
                    'title': 'Technical Architecture',
                    'description': 'System architecture and technology stack',
                    'required': True,
                    'order': 2,
                    'guiding_questions': [
                        'What is your system architecture?',
                        'What technology stack will you use?',
                        'How will you ensure scalability?',
                        'What are your security measures?',
                        'How will you handle data storage?'
                    ],
                    'ai_prompt': 'Design a robust technical architecture for a SaaS platform including technology stack, scalability, and security considerations.'
                },
                'subscription_model': {
                    'title': 'Subscription Model',
                    'description': 'Pricing tiers and subscription strategy',
                    'required': True,
                    'order': 3,
                    'guiding_questions': [
                        'What pricing tiers will you offer?',
                        'How will you structure your subscriptions?',
                        'What features are included in each tier?',
                        'How will you handle billing?',
                        'What is your pricing strategy?'
                    ],
                    'ai_prompt': 'Develop a comprehensive subscription model including pricing tiers, feature differentiation, and billing strategy.'
                },
                'customer_acquisition': {
                    'title': 'Customer Acquisition',
                    'description': 'How you will acquire and retain customers',
                    'required': True,
                    'order': 4,
                    'guiding_questions': [
                        'How will you acquire customers?',
                        'What is your customer acquisition cost?',
                        'How will you reduce churn?',
                        'What is your retention strategy?',
                        'How will you measure customer success?'
                    ],
                    'ai_prompt': 'Create a customer acquisition and retention strategy for a SaaS business including CAC, churn reduction, and success metrics.'
                }
            },
            'usage_count': 75,
            'completion_rate': 91.3,
            'is_premium': True
        }
    ]
    
    created_count = 0
    for template_data in templates:
        template, created = BusinessPlanTemplate.objects.get_or_create(
            name=template_data['name'],
            defaults=template_data
        )
        if created:
            created_count += 1
            print(f"✅ Created template: {template.name}")
        else:
            print(f"⚠️  Template already exists: {template.name}")
    
    print(f"\n🎉 Created {created_count} new templates!")
    print(f"📊 Total templates in database: {BusinessPlanTemplate.objects.count()}")

if __name__ == '__main__':
    create_sample_templates()
