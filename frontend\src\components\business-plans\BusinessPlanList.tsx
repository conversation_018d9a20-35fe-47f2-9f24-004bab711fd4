import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Download,
  Calendar,
  Clock,
  User,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  MoreVertical
} from 'lucide-react';
import {
  BusinessPlan,
  businessPlansAPI
} from '../../services/businessPlanApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';

interface BusinessPlanListProps {
  businessIdeaId?: number;
  onCreateNew?: () => void;
  onEdit?: (plan: BusinessPlan) => void;
  onView?: (plan: BusinessPlan) => void;
  onDelete?: (plan: BusinessPlan) => void;
}

const BusinessPlanList: React.FC<BusinessPlanListProps> = ({
  businessIdeaId,
  onCreateNew,
  onEdit,
  onView,
  onDelete
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // State management
  const [plans, setPlans] = useState<BusinessPlan[]>([]);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<number | null>(null);

  // Search and filter
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState<'updated_at' | 'created_at' | 'title'>('updated_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Dropdown state
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);

  useEffect(() => {
    loadData();
  }, [businessIdeaId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [plansData, businessIdeasData] = await Promise.all([
        businessPlansAPI.getPlans(businessIdeaId),
        businessIdeasAPI.getBusinessIdeas()
      ]);

      setPlans(plansData);
      setBusinessIdeas(businessIdeasData);
    } catch (err) {
      setError(t('businessPlan.loadError'));
      console.error('Error loading business plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadData();
  };

  const handleDelete = async (plan: BusinessPlan) => {
    if (!window.confirm(t('businessPlan.confirmDelete', { title: plan.title }))) {
      return;
    }

    try {
      setDeleting(plan.id);
      await businessPlansAPI.deletePlan(plan.id);
      setPlans(prev => prev.filter(p => p.id !== plan.id));
      setActiveDropdown(null);
      
      if (onDelete) {
        onDelete(plan);
      }
    } catch (err) {
      setError(t('businessPlan.deleteError'));
      console.error('Error deleting business plan:', err);
    } finally {
      setDeleting(null);
    }
  };

  const handleView = (plan: BusinessPlan) => {
    if (onView) {
      onView(plan);
    } else {
      navigate(`/dashboard/business-plans/${plan.id}`);
    }
  };

  const handleEdit = (plan: BusinessPlan) => {
    if (onEdit) {
      onEdit(plan);
    } else {
      navigate(`/dashboard/business-plans/${plan.id}/edit`);
    }
  };

  const handleCreateNew = () => {
    if (onCreateNew) {
      onCreateNew();
    } else {
      navigate('/dashboard/business-plans/create');
    }
  };

  // Filter and sort plans
  const filteredAndSortedPlans = plans
    .filter(plan => {
      const matchesSearch = plan.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (plan.business_idea_title && plan.business_idea_title.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = !statusFilter || plan.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'updated_at':
        default:
          aValue = new Date(a.updated_at).getTime();
          bValue = new Date(b.updated_at).getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit size={16} className="text-yellow-400" />;
      case 'review':
        return <Clock size={16} className="text-blue-400" />;
      case 'approved':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'needs_revision':
        return <AlertCircle size={16} className="text-red-400" />;
      default:
        return <FileText size={16} className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'review':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'approved':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'needs_revision':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar' : 'en', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{t('businessPlan.loadingPlans')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
      {/* Header */}
      <div className="p-6 border-b border-white/10">
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <FileText size={24} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('businessPlan.myPlans')}
              </h2>
              <p className="text-gray-300 text-sm">
                {t('businessPlan.managePlans')}
              </p>
            </div>
          </div>
          
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={handleRefresh}
              className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
              title={t('common.refresh')}
            >
              <RefreshCw size={16} />
            </button>
            <button
              onClick={handleCreateNew}
              className={`bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center ${
                isRTL ? 'flex-row-reverse' : ''
              }`}
            >
              <Plus size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              <span>{t('businessPlan.createNew')}</span>
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
          <div className="flex-1 relative">
            <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder={t('businessPlan.searchPlans')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full bg-gray-800 border border-gray-600 rounded-lg py-2 text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'
              }`}
            />
          </div>
          
          <div className="flex gap-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={`bg-gray-800 border border-gray-600 rounded-lg py-2 px-3 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                isRTL ? 'text-right' : ''
              }`}
            >
              <option value="">{t('businessPlan.allStatuses')}</option>
              <option value="draft">{t('businessPlan.status.draft')}</option>
              <option value="review">{t('businessPlan.status.review')}</option>
              <option value="approved">{t('businessPlan.status.approved')}</option>
              <option value="needs_revision">{t('businessPlan.status.needsRevision')}</option>
            </select>

            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as typeof sortBy);
                setSortOrder(order as typeof sortOrder);
              }}
              className={`bg-gray-800 border border-gray-600 rounded-lg py-2 px-3 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                isRTL ? 'text-right' : ''
              }`}
            >
              <option value="updated_at-desc">{t('businessPlan.sort.updatedDesc')}</option>
              <option value="updated_at-asc">{t('businessPlan.sort.updatedAsc')}</option>
              <option value="created_at-desc">{t('businessPlan.sort.createdDesc')}</option>
              <option value="created_at-asc">{t('businessPlan.sort.createdAsc')}</option>
              <option value="title-asc">{t('businessPlan.sort.titleAsc')}</option>
              <option value="title-desc">{t('businessPlan.sort.titleDesc')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-6 border-b border-white/10">
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertCircle size={20} className={`text-red-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <span className="text-red-300">{error}</span>
            </div>
          </div>
        </div>
      )}

      {/* Plans List */}
      <div className="p-6">
        {filteredAndSortedPlans.length === 0 ? (
          <div className="text-center py-12">
            <FileText size={48} className="mx-auto mb-4 text-gray-500" />
            <h3 className="text-lg font-semibold text-white mb-2">
              {searchTerm || statusFilter 
                ? t('businessPlan.noPlansFound')
                : t('businessPlan.noPlans')
              }
            </h3>
            <p className="text-gray-400 mb-6">
              {searchTerm || statusFilter
                ? t('businessPlan.tryDifferentSearch')
                : t('businessPlan.createFirstPlan')
              }
            </p>
            {!searchTerm && !statusFilter && (
              <button
                onClick={handleCreateNew}
                className={`bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors flex items-center mx-auto ${
                  isRTL ? 'flex-row-reverse' : ''
                }`}
              >
                <Plus size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span>{t('businessPlan.createNew')}</span>
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedPlans.map(plan => {
              const businessIdea = businessIdeas.find(idea => idea.id === plan.business_idea);

              return (
                <div
                  key={plan.id}
                  className="bg-gray-800/50 border border-gray-600 rounded-lg p-6 hover:border-purple-500/50 transition-all duration-200"
                >
                  <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-1">
                      <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <h3
                          className="text-lg font-semibold text-white cursor-pointer hover:text-purple-400 transition-colors"
                          onClick={() => handleView(plan)}
                        >
                          {plan.title}
                        </h3>
                        <div className={`flex items-center ${isRTL ? 'mr-3' : 'ml-3'}`}>
                          {getStatusIcon(plan.status)}
                          <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(plan.status)} ${isRTL ? 'mr-2' : 'ml-2'}`}>
                            {t(`businessPlan.status.${plan.status}`)}
                          </span>
                        </div>
                      </div>

                      {businessIdea && (
                        <p className="text-sm text-gray-400 mb-2">
                          {t('businessPlan.basedOn')}: {businessIdea.title}
                        </p>
                      )}

                      <div className={`flex items-center text-sm text-gray-400 space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Calendar size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          <span>{t('common.created')}: {formatDate(plan.created_at)}</span>
                        </div>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Clock size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          <span>{t('common.updated')}: {formatDate(plan.updated_at)}</span>
                        </div>
                        {plan.completion_percentage !== undefined && (
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <TrendingUp size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                            <span>{plan.completion_percentage}% {t('common.complete')}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions Dropdown */}
                    <div className="relative">
                      <button
                        onClick={() => setActiveDropdown(activeDropdown === plan.id ? null : plan.id)}
                        className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <MoreVertical size={16} />
                      </button>

                      {activeDropdown === plan.id && (
                        <div className={`absolute top-full mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10 min-w-[160px] ${
                          isRTL ? 'left-0' : 'right-0'
                        }`}>
                          <button
                            onClick={() => {
                              handleView(plan);
                              setActiveDropdown(null);
                            }}
                            className={`w-full px-4 py-2 text-left hover:bg-gray-700 transition-colors flex items-center text-white ${
                              isRTL ? 'flex-row-reverse text-right' : ''
                            }`}
                          >
                            <Eye size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span>{t('common.view')}</span>
                          </button>
                          <button
                            onClick={() => {
                              handleEdit(plan);
                              setActiveDropdown(null);
                            }}
                            className={`w-full px-4 py-2 text-left hover:bg-gray-700 transition-colors flex items-center text-white ${
                              isRTL ? 'flex-row-reverse text-right' : ''
                            }`}
                          >
                            <Edit size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span>{t('common.edit')}</span>
                          </button>
                          <button
                            onClick={() => {
                              // TODO: Implement download functionality
                              setActiveDropdown(null);
                            }}
                            className={`w-full px-4 py-2 text-left hover:bg-gray-700 transition-colors flex items-center text-white ${
                              isRTL ? 'flex-row-reverse text-right' : ''
                            }`}
                          >
                            <Download size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span>{t('common.download')}</span>
                          </button>
                          <hr className="border-gray-600" />
                          <button
                            onClick={() => handleDelete(plan)}
                            disabled={deleting === plan.id}
                            className={`w-full px-4 py-2 text-left hover:bg-red-600/20 transition-colors flex items-center text-red-400 disabled:opacity-50 disabled:cursor-not-allowed ${
                              isRTL ? 'flex-row-reverse text-right' : ''
                            }`}
                          >
                            {deleting === plan.id ? (
                              <div className={`w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                            ) : (
                              <Trash2 size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                            )}
                            <span>{t('common.delete')}</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessPlanList;
