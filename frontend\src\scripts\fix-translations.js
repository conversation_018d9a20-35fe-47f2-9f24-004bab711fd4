#!/usr/bin/env node

/**
 * Translation Fix Script
 * Automatically fixes common translation issues
 */

const fs = require('fs');
const path = require('path');

const LOCALES_DIR = path.join(__dirname, '../locales');
const SUPPORTED_LANGUAGES = ['en', 'ar'];

/**
 * Load translation file
 */
function loadTranslationFile(lang, filename) {
  const filePath = path.join(LOCALES_DIR, lang, filename);
  if (!fs.existsSync(filePath)) {
    console.warn(`⚠️  Translation file not found: ${filePath}`);
    return {};
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ Error loading ${filePath}:`, error.message);
    return {};
  }
}

/**
 * Save translation file
 */
function saveTranslationFile(lang, filename, data) {
  const filePath = path.join(LOCALES_DIR, lang, filename);
  const content = JSON.stringify(data, null, 2) + '\n';
  
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Saved: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error saving ${filePath}:`, error.message);
  }
}

/**
 * Extract all keys from nested object
 */
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        keys.push(...extractKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
  }
  
  return keys;
}

/**
 * Find missing keys between languages
 */
function findMissingKeys() {
  const translationFiles = ['common.json', 'ai.json', 'dashboard.json', 'businessPlan.json'];
  const missingKeys = {};
  
  SUPPORTED_LANGUAGES.forEach(lang => {
    missingKeys[lang] = [];
  });
  
  translationFiles.forEach(filename => {
    console.log(`\n🔍 Checking ${filename}...`);
    
    const translations = {};
    const allKeys = new Set();
    
    // Load translations for all languages
    SUPPORTED_LANGUAGES.forEach(lang => {
      translations[lang] = loadTranslationFile(lang, filename);
      const keys = extractKeys(translations[lang]);
      keys.forEach(key => allKeys.add(key));
    });
    
    // Find missing keys for each language
    SUPPORTED_LANGUAGES.forEach(lang => {
      const langKeys = new Set(extractKeys(translations[lang]));
      const missing = Array.from(allKeys).filter(key => !langKeys.has(key));
      
      if (missing.length > 0) {
        console.log(`  ❌ ${lang.toUpperCase()}: ${missing.length} missing keys`);
        missing.forEach(key => {
          console.log(`    - ${key}`);
          missingKeys[lang].push({ file: filename, key });
        });
      } else {
        console.log(`  ✅ ${lang.toUpperCase()}: Complete`);
      }
    });
  });
  
  return missingKeys;
}

/**
 * Fix duplicate keys in translation object
 */
function removeDuplicateKeys(obj, path = '') {
  const seen = new Set();
  const duplicates = [];
  
  function traverse(current, currentPath) {
    for (const key in current) {
      if (current.hasOwnProperty(key)) {
        const fullPath = currentPath ? `${currentPath}.${key}` : key;
        
        if (typeof current[key] === 'object' && current[key] !== null) {
          traverse(current[key], fullPath);
        } else {
          if (seen.has(fullPath)) {
            duplicates.push(fullPath);
          } else {
            seen.add(fullPath);
          }
        }
      }
    }
  }
  
  traverse(obj, path);
  return duplicates;
}

/**
 * Standardize translation key structure
 */
function standardizeKeyStructure() {
  console.log('\n🔧 Standardizing translation key structure...');
  
  const standardizations = [
    {
      file: 'ai.json',
      fixes: [
        {
          from: 'ai.automatically.fills',
          to: 'smart.formsDescription'
        },
        {
          from: 'automatically.generate.business',
          to: 'auto.contentDescription'
        }
      ]
    }
  ];
  
  standardizations.forEach(({ file, fixes }) => {
    SUPPORTED_LANGUAGES.forEach(lang => {
      const translations = loadTranslationFile(lang, file);
      let modified = false;
      
      fixes.forEach(({ from, to }) => {
        const value = getNestedValue(translations, from);
        if (value) {
          setNestedValue(translations, to, value);
          deleteNestedValue(translations, from);
          modified = true;
          console.log(`  ✅ ${lang}: ${from} → ${to}`);
        }
      });
      
      if (modified) {
        saveTranslationFile(lang, file, translations);
      }
    });
  });
}

/**
 * Get nested value from object
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Set nested value in object
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
}

/**
 * Delete nested value from object
 */
function deleteNestedValue(obj, path) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  const target = keys.reduce((current, key) => {
    return current && current[key] ? current[key] : null;
  }, obj);
  
  if (target && target.hasOwnProperty(lastKey)) {
    delete target[lastKey];
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🔧 Translation Fix Script\n');
  
  // Check for missing keys
  const missingKeys = findMissingKeys();
  
  // Standardize key structure
  standardizeKeyStructure();
  
  // Summary
  console.log('\n📊 Summary:');
  SUPPORTED_LANGUAGES.forEach(lang => {
    const total = missingKeys[lang].length;
    if (total > 0) {
      console.log(`  ${lang.toUpperCase()}: ${total} missing keys`);
    } else {
      console.log(`  ${lang.toUpperCase()}: ✅ Complete`);
    }
  });
  
  console.log('\n✅ Translation fix completed!');
}

// Run if this is the main module
if (require.main === module) {
  main();
}

module.exports = { findMissingKeys, standardizeKeyStructure };
