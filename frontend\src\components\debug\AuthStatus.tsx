/**
 * Authentication Status Debug Component
 * Shows current authentication state for debugging
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { getAuthToken, getRefreshToken } from '../../services/api';

interface TokenInfo {
  hasToken: boolean;
  hasRefreshToken: boolean;
  tokenExpired?: boolean;
  tokenPayload?: any;
}

export const AuthStatus: React.FC = () => {
  const { user, isAuthenticated, isLoading, error } = useAuth();
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>({ hasToken: false, hasRefreshToken: false });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateTokenInfo = () => {
      const token = getAuthToken();
      const refreshToken = getRefreshToken();
      
      let tokenPayload: any = null;
      let tokenExpired = false;
      
      if (token) {
        try {
          const parts = token.split('.');
          if (parts.length === 3) {
            tokenPayload = JSON.parse(atob(parts[1]));
            const now = Math.floor(Date.now() / 1000);
            tokenExpired = tokenPayload.exp < now;
          }
        } catch (e) {
          console.error('Error parsing token:', e);
        }
      }
      
      setTokenInfo({
        hasToken: !!token,
        hasRefreshToken: !!refreshToken,
        tokenExpired,
        tokenPayload
      });
    };

    updateTokenInfo();
    
    // Update every 5 seconds
    const interval = setInterval(updateTokenInfo, 5000);
    return () => clearInterval(interval);
  }, [user, isAuthenticated]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 bg-blue-600 text-white px-3 py-1 rounded text-xs z-50 hover:bg-blue-700"
      >
        🔐 Auth Debug
      </button>
    );
  }

  const getStatusIcon = (condition: boolean) => condition ? '✅' : '❌';
  const getStatusColor = (condition: boolean) => condition ? 'text-green-400' : 'text-red-400';

  return (
    <div className="fixed bottom-4 left-4 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs max-w-sm z-50 font-mono">
      <div className="flex justify-between items-center mb-3">
        <h4 className="font-bold text-sm">🔐 Auth Status</h4>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2">
        {/* Redux State */}
        <div className="border-b border-gray-600 pb-2">
          <div className="text-yellow-400 font-semibold mb-1">Redux State:</div>
          <div className={getStatusColor(isAuthenticated)}>
            {getStatusIcon(isAuthenticated)} Authenticated: {isAuthenticated ? 'Yes' : 'No'}
          </div>
          <div className={getStatusColor(!!user)}>
            {getStatusIcon(!!user)} User: {user ? user.username : 'None'}
          </div>
          <div className={getStatusColor(!isLoading)}>
            {getStatusIcon(!isLoading)} Loading: {isLoading ? 'Yes' : 'No'}
          </div>
          {error && (
            <div className="text-red-400">
              ❌ Error: {error}
            </div>
          )}
        </div>

        {/* Token State */}
        <div className="border-b border-gray-600 pb-2">
          <div className="text-yellow-400 font-semibold mb-1">Tokens:</div>
          <div className={getStatusColor(tokenInfo.hasToken)}>
            {getStatusIcon(tokenInfo.hasToken)} Access Token: {tokenInfo.hasToken ? 'Present' : 'Missing'}
          </div>
          <div className={getStatusColor(tokenInfo.hasRefreshToken)}>
            {getStatusIcon(tokenInfo.hasRefreshToken)} Refresh Token: {tokenInfo.hasRefreshToken ? 'Present' : 'Missing'}
          </div>
          {tokenInfo.tokenPayload && (
            <>
              <div className={getStatusColor(!tokenInfo.tokenExpired)}>
                {getStatusIcon(!tokenInfo.tokenExpired)} Token Status: {tokenInfo.tokenExpired ? 'Expired' : 'Valid'}
              </div>
              <div className="text-gray-300">
                Expires: {new Date(tokenInfo.tokenPayload.exp * 1000).toLocaleTimeString()}
              </div>
            </>
          )}
        </div>

        {/* Status Summary */}
        <div>
          <div className="text-yellow-400 font-semibold mb-1">Status:</div>
          {!tokenInfo.hasToken && !tokenInfo.hasRefreshToken && (
            <div className="text-orange-400">🔑 Not logged in</div>
          )}
          {tokenInfo.hasToken && tokenInfo.tokenExpired && (
            <div className="text-orange-400">⏰ Token expired</div>
          )}
          {tokenInfo.hasToken && !tokenInfo.tokenExpired && !isAuthenticated && (
            <div className="text-orange-400">🔄 Token valid but not authenticated</div>
          )}
          {isAuthenticated && tokenInfo.hasToken && !tokenInfo.tokenExpired && (
            <div className="text-green-400">✅ Fully authenticated</div>
          )}
          {isAuthenticated && !tokenInfo.hasToken && (
            <div className="text-red-400">⚠️ Authenticated but no token</div>
          )}
        </div>

        {/* Actions */}
        <div className="pt-2 border-t border-gray-600">
          <button
            onClick={() => {
              console.log('🔐 Current Auth State:', {
                redux: { isAuthenticated, user, isLoading, error },
                tokens: tokenInfo
              });
            }}
            className="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mr-2"
          >
            Log State
          </button>
          <button
            onClick={() => {
              if ((window as any).debugAuth) {
                (window as any).debugAuth();
              } else {
                console.log('debugAuth() not available');
              }
            }}
            className="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700"
          >
            Full Debug
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthStatus;
