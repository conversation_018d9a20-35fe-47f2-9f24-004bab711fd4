#!/usr/bin/env python
"""
Django management command to seed the database with sample business ideas
Usage: python manage.py seed_business_ideas
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from django.utils.text import slugify
from incubator.models import BusinessIdea, ProgressUpdate
from api.models import Tag
import random
from datetime import timedelta


class Command(BaseCommand):
    help = 'Seed the database with sample business ideas'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=25,
            help='Number of business ideas to create (default: 25)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing business ideas before seeding'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('🗑️  Clearing existing business ideas...')
            BusinessIdea.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('✅ Existing business ideas cleared'))

        count = options['count']
        self.stdout.write(f'🌱 Creating {count} sample business ideas...')

        # Ensure we have users to assign as owners
        users = list(User.objects.all())
        if not users:
            self.stdout.write(self.style.ERROR('❌ No users found. Please create users first.'))
            return

        # Create or get tags
        tag_names = [
            'AI', 'Machine Learning', 'Healthcare', 'Education', 'Fintech',
            'E-commerce', 'SaaS', 'Mobile App', 'Web Platform', 'IoT',
            'Blockchain', 'Sustainability', 'Food Tech', 'Travel', 'Gaming',
            'Social Media', 'Productivity', 'Security', 'Analytics', 'Automation'
        ]
        
        tags = []
        for tag_name in tag_names:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            tags.append(tag)
            if created:
                self.stdout.write(f'📌 Created tag: {tag_name}')

        # Sample business ideas data
        business_ideas_data = [
            {
                'title': 'AI-Powered Healthcare Diagnosis Assistant',
                'description': 'An AI system that helps doctors diagnose diseases faster and more accurately using machine learning algorithms trained on medical data.',
                'problem_statement': 'Medical diagnosis can be time-consuming and prone to human error, especially in areas with limited medical expertise.',
                'solution_description': 'Our AI assistant analyzes symptoms, medical history, and test results to provide diagnostic suggestions and confidence scores to healthcare professionals.',
                'target_audience': 'Healthcare providers, hospitals, and clinics in underserved areas',
                'market_opportunity': 'The global AI in healthcare market is expected to reach $102 billion by 2028',
                'business_model': 'SaaS subscription model for healthcare institutions with per-diagnosis pricing',
                'current_stage': 'development',
                'moderation_status': 'approved',
                'tags': ['AI', 'Healthcare', 'Machine Learning']
            },
            {
                'title': 'Smart Urban Farming Platform',
                'description': 'IoT-enabled vertical farming system that optimizes crop growth in urban environments using automated monitoring and control.',
                'problem_statement': 'Urban areas lack access to fresh, locally-grown produce, and traditional farming is not space-efficient.',
                'solution_description': 'Vertical farming towers with IoT sensors that monitor soil, water, light, and nutrients, automatically adjusting conditions for optimal growth.',
                'target_audience': 'Urban communities, restaurants, and grocery stores seeking fresh local produce',
                'market_opportunity': 'Urban farming market projected to reach $9.6 billion by 2026',
                'business_model': 'Equipment sales, subscription monitoring services, and produce sales',
                'current_stage': 'validation',
                'moderation_status': 'approved',
                'tags': ['IoT', 'Sustainability', 'Food Tech']
            },
            {
                'title': 'Blockchain-Based Digital Identity Verification',
                'description': 'Secure, decentralized identity verification system that gives users control over their personal data.',
                'problem_statement': 'Current identity verification systems are centralized, vulnerable to breaches, and give users no control over their data.',
                'solution_description': 'Blockchain-based system where users control their identity credentials and can selectively share verified information.',
                'target_audience': 'Financial institutions, government agencies, and online service providers',
                'market_opportunity': 'Digital identity market expected to reach $49.5 billion by 2026',
                'business_model': 'Transaction fees for verification services and enterprise licensing',
                'current_stage': 'concept',
                'moderation_status': 'pending',
                'tags': ['Blockchain', 'Security', 'Fintech']
            },
            {
                'title': 'Personalized Learning AI Tutor',
                'description': 'AI-powered educational platform that adapts to individual learning styles and provides personalized tutoring.',
                'problem_statement': 'Traditional education systems cannot provide personalized attention to each student\'s unique learning needs.',
                'solution_description': 'AI tutor that analyzes learning patterns, identifies knowledge gaps, and creates customized lesson plans and exercises.',
                'target_audience': 'Students, parents, and educational institutions',
                'market_opportunity': 'EdTech market projected to reach $377 billion by 2028',
                'business_model': 'Freemium model with premium features and institutional licenses',
                'current_stage': 'development',
                'moderation_status': 'approved',
                'tags': ['AI', 'Education', 'Machine Learning']
            },
            {
                'title': 'Sustainable Fashion Marketplace',
                'description': 'Online platform connecting consumers with sustainable fashion brands and promoting circular fashion economy.',
                'problem_statement': 'Fast fashion contributes to environmental damage, and consumers lack easy access to sustainable alternatives.',
                'solution_description': 'Curated marketplace featuring verified sustainable brands with transparency about materials, production, and environmental impact.',
                'target_audience': 'Environmentally conscious consumers and sustainable fashion brands',
                'market_opportunity': 'Sustainable fashion market expected to reach $15 billion by 2030',
                'business_model': 'Commission on sales, premium brand listings, and sustainability certification services',
                'current_stage': 'scaling',
                'moderation_status': 'approved',
                'tags': ['E-commerce', 'Sustainability', 'Web Platform']
            }
        ]

        # Add more diverse business ideas
        additional_ideas = [
            {
                'title': 'Mental Health Support Chatbot',
                'description': 'AI-powered chatbot providing 24/7 mental health support and crisis intervention.',
                'problem_statement': 'Mental health support is often inaccessible, expensive, and not available when needed most.',
                'solution_description': 'Intelligent chatbot trained on therapeutic techniques that provides immediate support and connects users to professional help when needed.',
                'target_audience': 'Individuals seeking mental health support, healthcare providers, and employers',
                'market_opportunity': 'Digital mental health market expected to reach $5.6 billion by 2026',
                'business_model': 'B2B2C partnerships with healthcare providers and direct consumer subscriptions',
                'current_stage': 'validation',
                'moderation_status': 'approved',
                'tags': ['AI', 'Healthcare', 'Mobile App']
            },
            {
                'title': 'Smart Home Energy Optimization',
                'description': 'IoT system that automatically optimizes home energy consumption to reduce costs and environmental impact.',
                'problem_statement': 'Homeowners waste energy and money due to inefficient energy usage patterns.',
                'solution_description': 'Smart system that learns usage patterns and automatically adjusts heating, cooling, and appliances for optimal efficiency.',
                'target_audience': 'Homeowners, property managers, and utility companies',
                'market_opportunity': 'Smart home market projected to reach $537 billion by 2030',
                'business_model': 'Hardware sales, installation services, and energy savings sharing',
                'current_stage': 'development',
                'moderation_status': 'approved',
                'tags': ['IoT', 'Sustainability', 'Automation']
            },
            {
                'title': 'Freelancer Skill Verification Platform',
                'description': 'Blockchain-based platform for verifying and showcasing freelancer skills and work history.',
                'problem_statement': 'Clients struggle to verify freelancer skills, and freelancers lack portable reputation systems.',
                'solution_description': 'Decentralized platform where completed work and client feedback create immutable skill credentials.',
                'target_audience': 'Freelancers, clients hiring freelancers, and gig economy platforms',
                'market_opportunity': 'Freelance market expected to reach $1.27 trillion by 2028',
                'business_model': 'Verification fees, premium profiles, and platform partnerships',
                'current_stage': 'concept',
                'moderation_status': 'pending',
                'tags': ['Blockchain', 'Web Platform', 'Productivity']
            }
        ]

        business_ideas_data.extend(additional_ideas)

        created_ideas = []
        
        for i in range(count):
            # Use predefined data if available, otherwise generate variations
            if i < len(business_ideas_data):
                idea_data = business_ideas_data[i].copy()
            else:
                # Generate variations of existing ideas
                base_idea = business_ideas_data[i % len(business_ideas_data)].copy()
                idea_data = base_idea
                idea_data['title'] = f"{base_idea['title']} - Variation {i - len(business_ideas_data) + 1}"
                idea_data['moderation_status'] = random.choice(['approved', 'pending', 'rejected'])
                idea_data['current_stage'] = random.choice(['concept', 'validation', 'development', 'scaling', 'established'])

            # Assign random owner
            owner = random.choice(users)
            
            # Create business idea
            business_idea = BusinessIdea.objects.create(
                title=idea_data['title'],
                description=idea_data['description'],
                problem_statement=idea_data['problem_statement'],
                solution_description=idea_data['solution_description'],
                target_audience=idea_data['target_audience'],
                market_opportunity=idea_data.get('market_opportunity', ''),
                business_model=idea_data.get('business_model', ''),
                current_stage=idea_data['current_stage'],
                owner=owner,
                moderation_status=idea_data['moderation_status'],
                moderated_by=random.choice(users) if idea_data['moderation_status'] != 'pending' else None,
                moderated_at=timezone.now() - timedelta(days=random.randint(1, 30)) if idea_data['moderation_status'] != 'pending' else None,
                created_at=timezone.now() - timedelta(days=random.randint(1, 90))
            )

            # Add tags
            idea_tags = [tag for tag in tags if tag.name in idea_data.get('tags', [])]
            if idea_tags:
                business_idea.tags.set(idea_tags)

            # Add random collaborators (0-3 collaborators)
            collaborator_count = random.randint(0, 3)
            if collaborator_count > 0:
                collaborators = random.sample([u for u in users if u != owner], min(collaborator_count, len(users) - 1))
                business_idea.collaborators.set(collaborators)

            created_ideas.append(business_idea)
            self.stdout.write(f'✅ Created: {business_idea.title} ({business_idea.moderation_status})')

        # Create progress updates for some business ideas
        self.stdout.write('\n📈 Creating progress updates...')
        progress_updates_created = 0
        
        for idea in created_ideas[:15]:  # Add progress updates to first 15 ideas
            update_count = random.randint(1, 4)
            for j in range(update_count):
                ProgressUpdate.objects.create(
                    business_idea=idea,
                    title=f'Progress Update #{j + 1}',
                    description=f'This is progress update #{j + 1} for {idea.title}',
                    achievements=f'Completed milestone {j + 1} including market research and prototype development',
                    challenges='Facing some technical challenges with implementation' if j > 0 else '',
                    next_steps=f'Next steps include testing and user feedback collection for milestone {j + 2}',
                    created_by=idea.owner,
                    created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                )
                progress_updates_created += 1

        self.stdout.write(self.style.SUCCESS(f'\n🎉 Successfully created:'))
        self.stdout.write(self.style.SUCCESS(f'   📝 {count} business ideas'))
        self.stdout.write(self.style.SUCCESS(f'   📈 {progress_updates_created} progress updates'))
        self.stdout.write(self.style.SUCCESS(f'   📌 {len(tags)} tags'))
        
        # Display statistics
        approved_count = BusinessIdea.objects.filter(moderation_status='approved').count()
        pending_count = BusinessIdea.objects.filter(moderation_status='pending').count()
        rejected_count = BusinessIdea.objects.filter(moderation_status='rejected').count()
        
        self.stdout.write(f'\n📊 Business Ideas Statistics:')
        self.stdout.write(f'   ✅ Approved: {approved_count}')
        self.stdout.write(f'   ⏳ Pending: {pending_count}')
        self.stdout.write(f'   ❌ Rejected: {rejected_count}')
        
        self.stdout.write(self.style.SUCCESS('\n🚀 Database seeding completed successfully!'))
