# Generated by Django 5.2.1 on 2025-07-15 03:22

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        (
            "incubator",
            "0016_rename_business_pl_busines_d3d3d3_idx_business_pl_busines_31df19_idx_and_more",
        ),
    ]

    operations = [
        migrations.RemoveField(
            model_name="templateperformancemetrics",
            name="template",
        ),
        migrations.AlterUniqueTogether(
            name="templaterecommendation",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="templaterecommendation",
            name="recommended_template",
        ),
        migrations.RemoveField(
            model_name="templaterecommendation",
            name="user",
        ),
        migrations.AlterUniqueTogether(
            name="templatesectionanalytics",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="templatesectionanalytics",
            name="template",
        ),
        migrations.RemoveField(
            model_name="templateusageanalytics",
            name="template",
        ),
        migrations.Remove<PERSON>ield(
            model_name="templateusageanalytics",
            name="user",
        ),
        migrations.RemoveField(
            model_name="usertemplateinteraction",
            name="template",
        ),
        migrations.RemoveField(
            model_name="usertemplateinteraction",
            name="user",
        ),
        migrations.DeleteModel(
            name="TemplateABTest",
        ),
        migrations.DeleteModel(
            name="TemplatePerformanceMetrics",
        ),
        migrations.DeleteModel(
            name="TemplateRecommendation",
        ),
        migrations.DeleteModel(
            name="TemplateSectionAnalytics",
        ),
        migrations.DeleteModel(
            name="TemplateUsageAnalytics",
        ),
        migrations.DeleteModel(
            name="UserTemplateInteraction",
        ),
    ]
