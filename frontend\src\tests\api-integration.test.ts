/**
 * API Integration Test Suite
 * Tests all backend API endpoints to ensure they return real data and handle errors correctly
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { businessPlansAPI } from '../services/businessPlanApi';
import { businessPlanAnalyticsAPI } from '../services/businessPlanAnalyticsApi';
import { investorDashboardAPI } from '../services/investorDashboardApi';
import { centralizedAiApi } from '../services/centralizedAiApi';
import { apiClient } from '../services/apiClient';

// Mock the API client for controlled testing
vi.mock('../services/apiClient');

describe('API Integration Tests', () => {
  beforeAll(() => {
    // Setup test environment
    vi.clearAllMocks();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  describe('Business Plans API', () => {
    it('should fetch business plans with real data structure', async () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            title: 'Tech Startup Plan',
            business_idea: 1,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-15T00:00:00Z',
            completion_percentage: 75,
            sections: [
              {
                id: 1,
                section_type: 'executive_summary',
                title: 'Executive Summary',
                content: 'Real executive summary content...',
                order: 1
              }
            ]
          }
        ]
      };

      (apiClient.get as any).mockResolvedValue(mockResponse);

      const result = await businessPlansAPI.getPlans(1);

      expect(apiClient.get).toHaveBeenCalledWith('/api/business-plans/', {
        params: { business_idea: 1 }
      });
      expect(result).toEqual(mockResponse.data);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('title');
      expect(result[0]).toHaveProperty('sections');
    });

    it('should create business plan with proper data', async () => {
      const planData = {
        title: 'New Business Plan',
        business_idea: 1,
        template_id: 1
      };

      const mockResponse = {
        data: {
          id: 2,
          ...planData,
          created_at: '2024-01-16T00:00:00Z',
          updated_at: '2024-01-16T00:00:00Z'
        }
      };

      (apiClient.post as any).mockResolvedValue(mockResponse);

      const result = await businessPlansAPI.createPlan(planData);

      expect(apiClient.post).toHaveBeenCalledWith('/api/business-plans/', planData);
      expect(result).toEqual(mockResponse.data);
      expect(result.id).toBe(2);
    });

    it('should handle API errors gracefully', async () => {
      (apiClient.get as any).mockRejectedValue(new Error('Network Error'));

      await expect(businessPlansAPI.getPlans(1)).rejects.toThrow('Network Error');
    });
  });

  describe('Business Plan Analytics API', () => {
    it('should fetch real analytics data', async () => {
      const mockAnalytics = {
        data: {
          timeSpent: {
            total: 120,
            thisWeek: 25,
            thisMonth: 95,
            daily: [2, 3, 4, 5, 3, 4, 4]
          },
          collaborationMetrics: {
            activeCollaborators: 3,
            totalSessions: 15,
            averageSessionDuration: 45
          },
          exportStatistics: {
            totalExports: 8,
            lastExport: '2024-01-15T10:30:00Z',
            exportsByFormat: {
              pdf: 5,
              word: 3
            }
          },
          userActivity: {
            dailyActive: 150,
            weeklyActive: 800,
            monthlyActive: 2500
          },
          completionMetrics: {
            averageCompletion: 68,
            sectionsCompleted: 156,
            totalSections: 230
          }
        }
      };

      (apiClient.get as any).mockResolvedValue(mockAnalytics);

      const result = await businessPlanAnalyticsAPI.getAnalytics(1);

      expect(apiClient.get).toHaveBeenCalledWith('/api/business-plans/1/analytics/');
      expect(result).toEqual(mockAnalytics.data);
      expect(result.timeSpent.total).toBe(120);
      expect(result.collaborationMetrics.activeCollaborators).toBe(3);
    });

    it('should fetch time tracking data', async () => {
      const mockTimeData = {
        data: {
          sessions: [
            {
              date: '2024-01-15',
              duration: 45,
              sections_worked: ['executive_summary', 'market_analysis']
            }
          ],
          totalTime: 120,
          averageSession: 30
        }
      };

      (apiClient.get as any).mockResolvedValue(mockTimeData);

      const result = await businessPlanAnalyticsAPI.getTimeTracking(1);

      expect(apiClient.get).toHaveBeenCalledWith('/api/business-plans/1/time-tracking/');
      expect(result.totalTime).toBe(120);
    });
  });

  describe('Dashboard APIs', () => {
    it('should fetch investor dashboard stats', async () => {
      const mockInvestorStats = {
        data: {
          portfolioValue: 2500000,
          totalInvestments: 15,
          activeDeals: 8,
          roi: 18.5,
          dealsThisMonth: 3,
          pendingReviews: 5,
          monthlyReturn: 2.8,
          irr: 28.5
        }
      };

      (apiClient.get as any).mockResolvedValue(mockInvestorStats);

      const result = await investorDashboardAPI.getDashboardStats();

      expect(apiClient.get).toHaveBeenCalledWith('/api/roles/investor/dashboard-stats/');
      expect(result.portfolioValue).toBe(2500000);
      expect(result.roi).toBe(18.5);
    });

    it('should fetch mentor dashboard stats', async () => {
      const mockMentorStats = {
        data: {
          totalMentees: 23,
          activeMentees: 8,
          upcomingSessions: 5,
          completedSessions: 156,
          averageRating: 4.8,
          responseRate: 95,
          monthlyEarnings: 3200
        }
      };

      (apiClient.get as any).mockResolvedValue(mockMentorStats);

      const result = await apiClient.get('/api/roles/mentor/dashboard-stats/');

      expect(result.data.totalMentees).toBe(23);
      expect(result.data.averageRating).toBe(4.8);
    });

    it('should fetch moderator dashboard stats', async () => {
      const mockModeratorStats = {
        data: {
          pendingReports: 12,
          flaggedContent: 8,
          activeUsers: 1247,
          totalReports: 156,
          resolvedToday: 23,
          averageResponseTime: 4.2,
          communityHealth: 94
        }
      };

      (apiClient.get as any).mockResolvedValue(mockModeratorStats);

      const result = await apiClient.get('/api/roles/moderator/dashboard-stats/');

      expect(result.data.pendingReports).toBe(12);
      expect(result.data.communityHealth).toBe(94);
    });
  });

  describe('AI Service API', () => {
    it('should generate content using real AI service', async () => {
      const mockAIResponse = {
        success: true,
        data: {
          content: 'AI-generated business plan content...',
          confidence: 0.85,
          processing_time: 2.3
        }
      };

      (apiClient.post as any).mockResolvedValue({ data: mockAIResponse });

      const result = await centralizedAiApi.generateIntelligentContent({
        content_type: 'business_plan_section',
        context: {
          section_type: 'executive_summary',
          business_idea: 'Tech startup'
        },
        language: 'en'
      });

      expect(apiClient.post).toHaveBeenCalledWith('/api/ai/generate-content/', {
        content_type: 'business_plan_section',
        context: {
          section_type: 'executive_summary',
          business_idea: 'Tech startup'
        },
        language: 'en'
      });
      expect(result.success).toBe(true);
      expect(result.data.content).toContain('AI-generated');
    });

    it('should check AI service status', async () => {
      const mockStatus = {
        data: {
          available: true,
          service: 'gemini',
          features: {
            content_generation: true,
            chat: true,
            business_analysis: true,
            multilingual: true
          },
          performance: {
            response_time: 1.2,
            success_rate: 0.98
          }
        }
      };

      (apiClient.get as any).mockResolvedValue(mockStatus);

      const result = await centralizedAiApi.getStatus();

      expect(apiClient.get).toHaveBeenCalledWith('/api/ai/status/');
      expect(result.available).toBe(true);
      expect(result.service).toBe('gemini');
    });
  });

  describe('Export API', () => {
    it('should export business plan as PDF', async () => {
      const mockPDFBlob = new Blob(['PDF content'], { type: 'application/pdf' });

      (apiClient.post as any).mockResolvedValue({
        data: mockPDFBlob,
        headers: {
          'content-type': 'application/pdf',
          'content-disposition': 'attachment; filename="business-plan.pdf"'
        }
      });

      const result = await businessPlansAPI.exportPlan(1, 'pdf');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/business-plans/1/export/',
        { format: 'pdf' },
        { responseType: 'blob' }
      );
      expect(result).toBeInstanceOf(Blob);
      expect(result.type).toBe('application/pdf');
    });

    it('should export business plan as Word document', async () => {
      const mockWordBlob = new Blob(['Word content'], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });

      (apiClient.post as any).mockResolvedValue({
        data: mockWordBlob,
        headers: {
          'content-type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'content-disposition': 'attachment; filename="business-plan.docx"'
        }
      });

      const result = await businessPlansAPI.exportPlan(1, 'word');

      expect(result).toBeInstanceOf(Blob);
      expect(result.type).toBe('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    });
  });

  describe('Error Handling', () => {
    it('should handle 500 server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal Server Error' }
        }
      };

      (apiClient.get as any).mockRejectedValue(serverError);

      await expect(businessPlansAPI.getPlans(1)).rejects.toMatchObject({
        response: {
          status: 500
        }
      });
    });

    it('should handle network errors', async () => {
      (apiClient.get as any).mockRejectedValue(new Error('Network Error'));

      await expect(businessPlanAnalyticsAPI.getAnalytics(1)).rejects.toThrow('Network Error');
    });

    it('should handle timeout errors', async () => {
      (apiClient.get as any).mockRejectedValue(new Error('timeout of 10000ms exceeded'));

      await expect(centralizedAiApi.getStatus()).rejects.toThrow('timeout');
    });
  });
});
