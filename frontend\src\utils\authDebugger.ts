/**
 * Authentication Debugger
 * Comprehensive debugging tool for authentication issues
 */

import { getAuthToken, getRefreshToken, authAPI } from '../services/api';
import { store } from '../store';

interface AuthDebugInfo {
  timestamp: string;
  tokens: {
    hasAccessToken: boolean;
    hasRefreshToken: boolean;
    accessTokenValid: boolean;
    accessTokenExpired: boolean;
    tokenPayload?: any;
  };
  redux: {
    isAuthenticated: boolean;
    hasUser: boolean;
    userInfo?: any;
    isLoading: boolean;
    error?: string;
  };
  api: {
    canCallProtectedEndpoint: boolean;
    lastApiError?: string;
  };
}

class AuthDebugger {
  private static instance: AuthDebugger;
  private debugHistory: AuthDebugInfo[] = [];

  static getInstance(): AuthDebugger {
    if (!AuthDebugger.instance) {
      AuthDebugger.instance = new AuthDebugger();
    }
    return AuthDebugger.instance;
  }

  /**
   * Get comprehensive authentication debug information
   */
  async getDebugInfo(): Promise<AuthDebugInfo> {
    const timestamp = new Date().toISOString();
    
    // Check tokens
    const accessToken = getAuthToken();
    const refreshToken = getRefreshToken();
    
    let tokenPayload: any = null;
    let accessTokenValid = false;
    let accessTokenExpired = false;
    
    if (accessToken) {
      try {
        const parts = accessToken.split('.');
        if (parts.length === 3) {
          tokenPayload = JSON.parse(atob(parts[1]));
          const now = Math.floor(Date.now() / 1000);
          accessTokenExpired = tokenPayload.exp < now;
          accessTokenValid = !accessTokenExpired;
        }
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }

    // Check Redux state
    const reduxState = store.getState();
    const authState = reduxState.auth;

    // Test API call
    let canCallProtectedEndpoint = false;
    let lastApiError: string | undefined;
    
    try {
      await authAPI.getCurrentUser();
      canCallProtectedEndpoint = true;
    } catch (error: any) {
      lastApiError = error?.message || 'Unknown API error';
    }

    const debugInfo: AuthDebugInfo = {
      timestamp,
      tokens: {
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        accessTokenValid,
        accessTokenExpired,
        tokenPayload
      },
      redux: {
        isAuthenticated: authState.isAuthenticated,
        hasUser: !!authState.user,
        userInfo: authState.user ? {
          id: authState.user.id,
          username: authState.user.username,
          role: authState.user.role
        } : null,
        isLoading: authState.isLoading,
        error: authState.error
      },
      api: {
        canCallProtectedEndpoint,
        lastApiError
      }
    };

    this.debugHistory.push(debugInfo);
    
    // Keep only last 10 entries
    if (this.debugHistory.length > 10) {
      this.debugHistory = this.debugHistory.slice(-10);
    }

    return debugInfo;
  }

  /**
   * Print debug information to console
   */
  async printDebugInfo(): Promise<void> {
    const info = await this.getDebugInfo();
    
    console.group('🔐 Authentication Debug Info');
    console.log('Timestamp:', info.timestamp);
    
    console.group('📝 Tokens');
    console.log('Access Token:', info.tokens.hasAccessToken ? '✅ Present' : '❌ Missing');
    console.log('Refresh Token:', info.tokens.hasRefreshToken ? '✅ Present' : '❌ Missing');
    console.log('Token Valid:', info.tokens.accessTokenValid ? '✅ Valid' : '❌ Invalid');
    console.log('Token Expired:', info.tokens.accessTokenExpired ? '⚠️ Expired' : '✅ Not Expired');
    if (info.tokens.tokenPayload) {
      console.log('Token Payload:', {
        user_id: info.tokens.tokenPayload.user_id,
        exp: new Date(info.tokens.tokenPayload.exp * 1000).toLocaleString(),
        iat: new Date(info.tokens.tokenPayload.iat * 1000).toLocaleString()
      });
    }
    console.groupEnd();
    
    console.group('🏪 Redux State');
    console.log('Is Authenticated:', info.redux.isAuthenticated ? '✅ Yes' : '❌ No');
    console.log('Has User:', info.redux.hasUser ? '✅ Yes' : '❌ No');
    console.log('Is Loading:', info.redux.isLoading ? '⏳ Yes' : '✅ No');
    if (info.redux.userInfo) {
      console.log('User Info:', info.redux.userInfo);
    }
    if (info.redux.error) {
      console.log('Redux Error:', info.redux.error);
    }
    console.groupEnd();
    
    console.group('🌐 API Status');
    console.log('Can Call Protected Endpoint:', info.api.canCallProtectedEndpoint ? '✅ Yes' : '❌ No');
    if (info.api.lastApiError) {
      console.log('Last API Error:', info.api.lastApiError);
    }
    console.groupEnd();
    
    // Provide recommendations
    console.group('💡 Recommendations');
    if (!info.tokens.hasAccessToken && !info.tokens.hasRefreshToken) {
      console.log('🔑 No tokens found - User needs to log in');
    } else if (info.tokens.accessTokenExpired && info.tokens.hasRefreshToken) {
      console.log('🔄 Access token expired - Should attempt refresh');
    } else if (info.tokens.hasAccessToken && !info.redux.isAuthenticated) {
      console.log('🔄 Token exists but Redux not authenticated - Should fetch current user');
    } else if (info.redux.isAuthenticated && !info.api.canCallProtectedEndpoint) {
      console.log('⚠️ Redux authenticated but API calls failing - Token may be invalid');
    }
    console.groupEnd();
    
    console.groupEnd();
  }

  /**
   * Get debug history
   */
  getHistory(): AuthDebugInfo[] {
    return [...this.debugHistory];
  }

  /**
   * Clear debug history
   */
  clearHistory(): void {
    this.debugHistory = [];
  }

  /**
   * Auto-debug on authentication errors
   */
  onAuthError(error: any, context?: string): void {
    console.error(`🚨 Authentication Error${context ? ` in ${context}` : ''}:`, error);
    this.printDebugInfo();
  }

  /**
   * Monitor authentication state changes
   */
  startMonitoring(): void {{
    // Monitor Redux state changes
    let previousAuthState = store.getState().auth;
    
    store.subscribe(() => {
      const currentAuthState = store.getState().auth;
      
      if (currentAuthState !== previousAuthState) {
        console.log('🔄 Auth state changed:', {
          wasAuthenticated: previousAuthState.isAuthenticated,
          nowAuthenticated: currentAuthState.isAuthenticated,
          hadUser: !!previousAuthState.user,
          hasUser: !!currentAuthState.user,
          wasLoading: previousAuthState.isLoading,
          isLoading: currentAuthState.isLoading,
          hadError: !!previousAuthState.error,
          hasError: !!currentAuthState.error
        });
        
        previousAuthState = currentAuthState;
      }
    });

    // Monitor localStorage changes
    window.addEventListener('storage', (event) => {
      if (event.key === 'yasmeen_auth_token' || event.key === 'yasmeen_refresh_token') {
        console.log('🔄 Token storage changed:', {
          key: event.key,
          oldValue: event.oldValue ? 'Present' : 'Null',
          newValue: event.newValue ? 'Present' : 'Null'
        });
        this.printDebugInfo();
      }
    });

    console.log('🔍 Authentication monitoring started');
  }
}

// Export singleton instance
export const authDebugger = AuthDebugger.getInstance();

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
  authDebugger.startMonitoring();
  
  // Add global debug function
  (window as any).debugAuth = () => authDebugger.printDebugInfo();
  console.log('🔍 Auth debugger loaded. Use debugAuth() in console for manual debugging.');
}

export default AuthDebugger;
