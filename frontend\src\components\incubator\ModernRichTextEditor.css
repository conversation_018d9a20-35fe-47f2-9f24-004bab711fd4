/* Modern Rich Text Editor Styles */
.modern-rich-text-editor {
  @apply rounded-lg overflow-hidden shadow-sm;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-rich-text-editor.rtl {
  direction: rtl;
}

.editor-toolbar {
  background: rgba(255, 255, 255, 0.05);
}

.toolbar-group {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding-right: 8px;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toolbar-button:active {
  transform: translateY(0);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  overflow-y: auto;
  max-height: 500px;
}

.editor-content:empty::before {
  content: attr(data-placeholder);
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  pointer-events: none;
}

.editor-content:focus {
  box-shadow: inset 0 0 0 2px rgba(147, 51, 234, 0.5);
}

/* Content styling */
.editor-content h1 {
  @apply text-2xl font-bold mb-4 text-white;
}

.editor-content h2 {
  @apply text-xl font-semibold mb-3 text-white;
}

.editor-content h3 {
  @apply text-lg font-medium mb-2 text-white;
}

.editor-content p {
  @apply mb-3 text-white/90;
}

.editor-content ul, .editor-content ol {
  @apply mb-3 pl-6;
}

.editor-content li {
  @apply mb-1 text-white/90;
}

.editor-content blockquote {
  @apply border-l-4 border-purple-500 pl-4 italic text-white/80 mb-3;
}

.editor-content a {
  @apply text-purple-400 hover:text-purple-300 underline;
}

.editor-content strong {
  @apply font-bold text-white;
}

.editor-content em {
  @apply italic text-white/90;
}

.editor-content u {
  @apply underline text-white/90;
}

/* RTL specific styles */
.modern-rich-text-editor.rtl .editor-content ul,
.modern-rich-text-editor.rtl .editor-content ol {
  padding-right: 1.5rem;
  padding-left: 0;
}

.modern-rich-text-editor.rtl .editor-content blockquote {
  border-right: 4px solid rgb(147, 51, 234);
  border-left: none;
  padding-right: 1rem;
  padding-left: 0;
}

.modern-rich-text-editor.rtl .toolbar-group {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  border-right: none;
  padding-left: 8px;
  padding-right: 0;
}

/* Accessibility improvements */
.toolbar-button:focus {
  outline: 2px solid rgba(147, 51, 234, 0.8);
  outline-offset: 2px;
}

.editor-content:focus {
  outline: none;
}

/* Animation for auto-save indicator */
.auto-save-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(34, 197, 94, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 10;
}

.auto-save-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

.auto-save-indicator.error {
  background: rgba(239, 68, 68, 0.9);
}

.modern-rich-text-editor.rtl .auto-save-indicator {
  right: auto;
  left: 8px;
}

/* Loading state */
.editor-loading {
  position: relative;
}

.editor-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Error state */
.editor-error {
  border-color: rgba(239, 68, 68, 0.5);
}

.editor-error .editor-content {
  background: rgba(239, 68, 68, 0.05);
}