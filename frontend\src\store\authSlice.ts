import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, authAPI, getAuthToken, clearAuthTokens } from '../services/api';

// Define the state interface
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async ({ username, password }: { username: string; password: string }, { rejectWithValue }) => {
    try {
      const user = await authAPI.login(username, password);
      return user;
    } catch (err: any) {

      // Extract meaningful error message
      let errorMessage = 'Login failed';
      if (err?.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      console.error('Login error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout();
      return null;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Logout failed');
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: {
    username: string;
    email: string;
    password: string;
    password_confirm: string;
    first_name?: string;
    last_name?: string;
  }, { rejectWithValue }) => {
    try {
      console.log('Registering user with data:', {
        ...userData,
        password: '***',
        password_confirm: '***'
      });

      const user = await authAPI.register(userData);
      console.log('Registration successful:', user);
      return user;
    } catch (err: any) {
      console.error('Registration error details:', err);

      // Extract meaningful error message
      let errorMessage = 'Registration failed';
      if (err?.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.response?.data?.username) {
        errorMessage = `Username: ${err.response.data.username[0]}`;
      } else if (err?.response?.data?.email) {
        errorMessage = `Email: ${err.response.data.email[0]}`;
      } else if (err?.response?.data?.password) {
        errorMessage = `Password: ${err.response.data.password[0]}`;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      console.error('Registration error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Check if we have a token first using the correct token key
      const token = getAuthToken();
      if (!token) {
        if (process.env.NODE_ENV === 'development') {
          console.log('No access token found');
        }
        // Clear auth state if no token
        dispatch(authSlice.actions.clearAuth());
        return rejectWithValue('No authentication token');
      }

      const user = await authAPI.getCurrentUser();
      if (process.env.NODE_ENV === 'development') {
        console.log('Current user fetched successfully:', user);
      }
      return user;
    } catch (err: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching current user:', err);
      }

      // Handle authentication errors gracefully
      if (err?.status === 401) {
        clearAuthTokens();
        // Clear Redux auth state on 401
        dispatch(authSlice.actions.clearAuth());
      }

      return rejectWithValue(err?.message || 'Authentication failed');
    }
  }
);

// Create the slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAuth: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
      state.isLoading = false;
    },
    setAuthState: (state, action: PayloadAction<{ user: User; isAuthenticated: boolean }>) => {
      state.user = action.payload.user;
      state.isAuthenticated = action.payload.isAuthenticated;
      state.error = null;
      state.isLoading = false;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder.addCase(login.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(login.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(login.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Logout
    builder.addCase(logout.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(logout.fulfilled, (state) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
    });
    builder.addCase(logout.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Register
    builder.addCase(register.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(register.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(register.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Get current user
    builder.addCase(getCurrentUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(getCurrentUser.fulfilled, (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    });
    builder.addCase(getCurrentUser.rejected, (state) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
    });
  },
});

// Export actions and reducer
export const { clearError } = authSlice.actions;
export default authSlice.reducer;
