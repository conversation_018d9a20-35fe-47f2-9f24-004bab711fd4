/**
 * Business Plan Data Flow Integration Tests
 * Tests the coordination between Redux, React Query, and backend API
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { businessPlansSlice } from '../store/businessPlansSlice';
import { useBusinessPlanDataFlow } from '../hooks/useBusinessPlanDataFlow';
import { businessPlanSyncService } from '../services/businessPlanSyncService';
import { businessPlansAPI, businessPlanSectionsAPI } from '../services/businessPlanApi';

// Mock the APIs
vi.mock('../services/businessPlanApi');
vi.mock('../services/businessPlanSyncService');

const mockBusinessPlan = {
  id: 1,
  title: 'Test Business Plan',
  business_idea: 1,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  sections: []
};

const mockSections = [
  {
    id: 1,
    business_plan: 1,
    section_type: 'executive_summary',
    title: 'Executive Summary',
    content: 'Test content',
    order: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

describe('Business Plan Data Flow', () => {
  let queryClient: QueryClient;
  let store: any;
  let wrapper: any;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    store = configureStore({
      reducer: {
        businessPlans: businessPlansSlice.reducer
      }
    });

    wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      </Provider>
    );

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    queryClient.clear();
  });

  describe('useBusinessPlanDataFlow', () => {
    it('should fetch business plan and sections on mount', async () => {
      // Mock API responses
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);
      (businessPlanSectionsAPI.getSections as any).mockResolvedValue(mockSections);

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.businessPlan).toEqual(mockBusinessPlan);
      expect(result.current.sections).toEqual(mockSections);
      expect(businessPlansAPI.getPlan).toHaveBeenCalledWith(1);
      expect(businessPlanSectionsAPI.getSections).toHaveBeenCalledWith(1);
    });

    it('should sync React Query data to Redux', async () => {
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);
      (businessPlanSectionsAPI.getSections as any).mockResolvedValue(mockSections);

      renderHook(() => useBusinessPlanDataFlow(1), { wrapper });

      await waitFor(() => {
        const state = store.getState().businessPlans;
        expect(state.currentBusinessPlan).toEqual(mockBusinessPlan);
        expect(state.sections).toEqual(mockSections);
      });
    });

    it('should handle optimistic updates for section changes', async () => {
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);
      (businessPlanSectionsAPI.getSections as any).mockResolvedValue(mockSections);
      (businessPlanSectionsAPI.updateSection as any).mockResolvedValue({
        ...mockSections[0],
        content: 'Updated content'
      });

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Perform update
      await result.current.updateSection(1, { content: 'Updated content' });

      // Check that the update was applied
      expect(businessPlanSectionsAPI.updateSection).toHaveBeenCalledWith(1, {
        content: 'Updated content'
      });
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('API Error');
      (businessPlansAPI.getPlan as any).mockRejectedValue(error);

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.error).toBeTruthy();
      });

      expect(result.current.businessPlan).toBeUndefined();
    });
  });

  describe('Data Synchronization', () => {
    it('should sync data between Redux and React Query', async () => {
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);
      (businessPlanSectionsAPI.getSections as any).mockResolvedValue(mockSections);

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Verify data is in both Redux and React Query
      const reduxState = store.getState().businessPlans;
      expect(reduxState.currentBusinessPlan).toEqual(mockBusinessPlan);
      expect(reduxState.sections).toEqual(mockSections);

      const queryData = queryClient.getQueryData(['businessPlans', 'detail', 1]);
      expect(queryData).toEqual(mockBusinessPlan);
    });

    it('should invalidate cache when data changes', async () => {
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);
      (businessPlanSectionsAPI.getSections as any).mockResolvedValue(mockSections);
      (businessPlanSectionsAPI.updateSection as any).mockResolvedValue({
        ...mockSections[0],
        content: 'Updated content'
      });

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Update section
      await result.current.updateSection(1, { content: 'Updated content' });

      // Verify cache invalidation was triggered
      await waitFor(() => {
        expect(businessPlansAPI.getPlan).toHaveBeenCalledTimes(2); // Initial + refetch
      });
    });
  });

  describe('Business Plan Sync Service', () => {
    beforeEach(() => {
      businessPlanSyncService.clearSyncState();
    });

    it('should prevent duplicate sync operations', async () => {
      const mockSync = vi.fn().mockResolvedValue(undefined);
      (businessPlanSyncService.syncBusinessPlan as any) = mockSync;

      // Start multiple sync operations
      const promises = [
        businessPlanSyncService.syncBusinessPlan(1),
        businessPlanSyncService.syncBusinessPlan(1),
        businessPlanSyncService.syncBusinessPlan(1)
      ];

      await Promise.all(promises);

      // Should only call sync once due to deduplication
      expect(mockSync).toHaveBeenCalledTimes(1);
    });

    it('should handle auto-save conflicts', async () => {
      const mockConflictCheck = vi.fn().mockResolvedValue({
        hasConflict: true,
        backendContent: 'Backend content'
      });
      
      (businessPlanSyncService.handleAutoSaveConflict as any) = mockConflictCheck;

      const result = await businessPlanSyncService.handleAutoSaveConflict(
        1, 
        'Local content'
      );

      expect(result.hasConflict).toBe(true);
      expect(result.backendContent).toBe('Backend content');
    });
  });

  describe('Auto-save Race Conditions', () => {
    it('should prevent auto-save during manual save', async () => {
      // This would be tested with the actual auto-save hook
      // For now, we test the concept with the sync service
      
      const syncStatus = businessPlanSyncService.getSyncStatus();
      expect(syncStatus.inProgress).toEqual([]);
      expect(syncStatus.lastSyncTimes).toEqual({});
    });
  });

  describe('Cache Management', () => {
    it('should invalidate all caches when requested', async () => {
      const invalidateSpy = vi.spyOn(queryClient, 'invalidateQueries');
      
      await businessPlanSyncService.invalidateAllCaches();

      expect(invalidateSpy).toHaveBeenCalledWith({
        queryKey: ['businessPlans']
      });
      expect(invalidateSpy).toHaveBeenCalledWith({
        queryKey: ['businessPlan']
      });
    });

    it('should handle stale data correctly', async () => {
      (businessPlansAPI.getPlan as any).mockResolvedValue(mockBusinessPlan);

      const { result } = renderHook(
        () => useBusinessPlanDataFlow(1),
        { wrapper }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Simulate stale data by updating the mock
      const updatedPlan = { ...mockBusinessPlan, title: 'Updated Title' };
      (businessPlansAPI.getPlan as any).mockResolvedValue(updatedPlan);

      // Refresh data
      await result.current.refreshData();

      await waitFor(() => {
        expect(result.current.businessPlan?.title).toBe('Updated Title');
      });
    });
  });
});
