"""
AI Models app - Django models for AI functionality
"""
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class AIModelMetadata(models.Model):
    """Metadata for AI models used in the system"""
    
    name = models.Char<PERSON>ield(max_length=100, unique=True)
    version = models.CharField(max_length=20)
    description = models.TextField()
    model_type = models.CharField(max_length=50)  # 'classifier', 'predictor', etc.
    file_path = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "AI Model Metadata"
        verbose_name_plural = "AI Model Metadata"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} v{self.version}"


class PredictionLog(models.Model):
    """Log of AI predictions made by the system"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    model_name = models.CharField(max_length=100)
    input_data = models.JSONField()
    prediction_result = models.JSONField()
    confidence_score = models.FloatField(null=True, blank=True)
    processing_time = models.FloatField(help_text="Processing time in seconds")
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Prediction Log"
        verbose_name_plural = "Prediction Logs"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.model_name} prediction for {self.user or 'Anonymous'}"
