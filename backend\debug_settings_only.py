#!/usr/bin/env python
"""
Test Django settings without app loading
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(backend_dir))

print("🔍 Testing Django Settings Only")

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')

try:
    print("1. Loading environment...")
    from dotenv import load_dotenv
    load_dotenv(backend_dir / '.env')
    print("✅ Environment loaded")
except Exception as e:
    print(f"❌ Environment failed: {e}")

try:
    print("2. Importing Django...")
    import django
    print(f"✅ Django imported: {django.get_version()}")
except Exception as e:
    print(f"❌ Django import failed: {e}")

try:
    print("3. Loading Django settings...")
    from django.conf import settings
    print(f"✅ Settings loaded, DEBUG={settings.DEBUG}")
    print(f"   Database: {settings.DATABASES['default']['ENGINE']}")
    print(f"   Installed apps: {len(settings.INSTALLED_APPS)} apps")
except Exception as e:
    print(f"❌ Settings failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("4. Testing individual app imports...")
    
    # Test each app individually
    apps_to_test = [
        'core',
        'api', 
        'users',
        'search',
        'incubator',
        'ai_recommendations',
        'ai_core',
        'ai_models',
        'forums',
        'superadmin'
    ]
    
    for app_name in apps_to_test:
        try:
            __import__(app_name)
            print(f"   ✅ {app_name}")
        except Exception as e:
            print(f"   ❌ {app_name}: {e}")
            
except Exception as e:
    print(f"❌ App testing failed: {e}")

print("\n🎉 Settings test complete!")
